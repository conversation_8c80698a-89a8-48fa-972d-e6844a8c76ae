# mlog guide

## 概述

---

    MINIEYE log（简称 mlog）是MINIEYE提供的日志管理库，具有以下的功能和特性：

- 支持log分级输出
- 多线程安全
- 支持log file文件的生成
- 高性能的log输出
- 多种样式的输出宏和方法封装
- 可配置同步syslog输出
- 支持远端dlt-daemon输出或者logd输出
- 可配置异步或者同步方式输出
- 提供mlogcat工具，支持系统全局实时查看log，并提供方便的log过滤方法
- 动态配置log等级
- 全局统一配置log等级

![mlog-1](mlog-1.png)

## 模块依赖

---

    spdlog 1.9.2

    logd

    如果使用其他版本的库，请确认编译和测试用例可以通过

## 工具

---

    目前提供了mlogcat和mlogdeobf工具，它可以

- 帮助系统全局实时查看log，并提供方便的log过滤方法
- 动态配置log等级

    **程序必须配置使用logd输出，并且系统中有运行logd守护程序，才可以支持mlogcat的使用**

    mlocagt的用法如下：

```

options include:
  -l            Set log level
  -o            Output obfuscated logs (encrypted for privacy)
  -s              Set default filter to silent. Equivalent to filterspec '*:S'
  -f <file>, --file=<file>               Log to file. Default is stdout
  -r <kbytes>, --rotate-kbytes=<kbytes>
                  Rotate log every kbytes. Requires -f option
  -n <count>, --rotate-count=<count>
                  Sets max number of rotated logs to <count>, default 4
  -v <format>, --format=<format>
                  Sets the log print format, where <format> is:
                    brief color epoch long monotonic printable process raw
                    tag thread threadtime time uid usec UTC year zone
  -D, --dividers  Print dividers between each log buffer
  -c, --clear     Clear (flush) the entire log and exit
                  if Log to File specified, clear fileset instead
  -d              Dump the log and then exit (don't block)
  -e <expr>, --regex=<expr>
                  Only print lines where the log message matches <expr>
                  where <expr> is a regular expression
  -m <count>, --max-count=<count>
                  Quit after printing <count> lines. This is meant to be
                  paired with --regex, but will work on its own.
  --print         Paired with --regex and --max-count to let content bypass
                  regex filter but still stop at number of matches.
  -t <count>      Print only the most recent <count> lines (implies -d)
  -t '<time>'     Print most recent lines since specified time (implies -d)
  -T <count>      Print only the most recent <count> lines (does not imply -d)
  -T '<time>'     Print most recent lines since specified time (not imply -d)
                  count is pure numerical, time is 'MM-DD hh:mm:ss.mmm...'
                  'YYYY-MM-DD hh:mm:ss.mmm...' or 'sssss.mmm...' format
  -g, --buffer-size                      Get the size of the ring buffer.
  -G <size>, --buffer-size=<size>
                  Set size of log ring buffer, may suffix with K or M.
  -L, -last       Dump logs from prior to last reboot
  -b <buffer>, --buffer=<buffer>         Request alternate ring buffer, 'main',
                  'system', 'radio', 'events', 'crash', 'default' or 'all'.
                  Multiple -b parameters or comma separated list of buffers are
                  allowed. Buffers interleaved. Default -b main,system,crash.
  -B, --binary    Output the log in binary.
  -S, --statistics                       Output statistics.
  -p, --prune     Print prune white and ~black list. Service is specified as
                  UID, UID/PID or /PID. Weighed for quicker pruning if prefix
                  with ~, otherwise weighed for longevity if unadorned. All
                  other pruning activity is oldest first. Special case ~!
                  represents an automatic quicker pruning for the noisiest
                  UID as determined by the current statistics.
  -P '<list> ...', --prune='<list> ...'
                  Set prune white and ~black list, using same format as
                  listed above. Must be quoted.
  --pid=<pid>     Only prints logs from the given pid.
  --wrap          Sleep for 2 hours or when buffer about to wrap whichever
                  comes first. Improves efficiency of polling by providing
                  an about-to-wrap wakeup.

filterspecs are a series of 
  <tag>[:priority]

where <tag> is a log component tag (or * for all) and priority is:
  T    Verbose (default for <tag>)
  D    Debug (default for '*')
  I    Info
  W    Warn
  E    Error
  C    Critical
  S    Silent (suppress all output)

'*' by itself means '*:D' and <tag> by itself means <tag>:V.
If no '*' filterspec or -s on command line, all filter defaults to '*:V'.
eg: '*:S <tag>' prints only <tag>, '<tag>:S' suppresses all <tag> log messages.

If not specified on the command line, filterspec is set from ANDROID_LOG_TAGS.

If not specified with -v on command line, format is set from ANDROID_PRINTF_LOG
or defaults to "threadtime"

```

使用mlogcat可以直接打印全局log。

使用mlogcat -s tag可以只打印tag模块的log，其中tag是在mlog模块初始化传的LogConfig中的tag字符串，下同。

使用mlogcat -s tag:priority 可以过于tag模块的priority等级和以上的log

使用mlogcat -l tag:priority 可以动态设置tag模块的日志等级为priority

使用mlogcat -l *:priority 可以动态设置所有模块的日志等级为priority

使用mlogcat -o 可以输出混淆后的日志，用于保护敏感信息

声明MLOG_LOG_LEVEL可以全局配置log等级：

例如：export MLOG_LOG_LEVEL=I  ，全局配置所有模块的初始log等级为Info。

## 注意

---

- 在配置mlog的时候，如果选择isAsync = true; isAsyncBlock = false，则在高速刷写log的时候，可能会丢。
- 使能了dlt输出，但是系统中没有开守护进程dlt-daemon。在进程退出的时候，会有一段时间开在回收dlt，主要是等待dlt可以写然后把缓存的log刷出去。时间为3秒钟
- 如果程序中打印一行字符中间有\n符号，在mlogcat打印全局log被换成空格，在终端打印则会按换行来输出。
- 使用mlogcat命令控制日志等级，仅仅会生效于正在运行的程序，当程序重启之后，还是按程序设置的默认等级输出。
- 尽量不要在量产设备出现记录Log文件的情况，避免设备频繁刷写flash，影响设备寿命。
- 记录log底层使用的是fwrite，C库有缓冲。mlog库内部会有个线程专门做5s的flush操作


## 日志混淆功能

---

mlog提供了日志混淆功能，用于保护敏感信息，防止日志内容被直接读取。

### 混淆原理

日志混淆采用混合加密方案：
- **RSA3072加密**: 用于保护AES密钥，RSA密钥经过多层混淆算法处理后内置在程序中
- **AES-128-GCM加密**: 用于加密实际的日志内容，提供加密和完整性验证
- **动态密钥**: 每次运行都会生成新的AES密钥，提高安全性

### 使用方法

**生成混淆日志**：
```bash
mlogcat -o > encrypted.log
```

**解密日志**：
```bash
# 方法1：管道方式
cat encrypted.log | mlogdeobf

# 方法2：文件输入方式
mlogdeobf encrypted.log

# 方法3：实时解密
mlogcat -o | mlogdeobf
```

### 注意事项

- 混淆日志的第一行包含加密的AES密钥，删除后将无法解密
- 每次运行`mlogcat -o`都会生成不同的密钥，无法交叉解密
- 混淆功能会增加一定的性能开销，建议仅在需要时使用
- 解密需要使用配套的`mlogdeobf`工具

mlogdeobf是mlogcat -o命令专用的解混淆工具，支持管道用法和文件输入