import("//build/minieye/minieye.gni")

MLOG_WITH_DLT = "false"
MLOG_WITH_LOGD = "false"
MLOG_WITH_FILE_MINIEYE = "false"
MLOG_WITH_OBFUCATED = "false"
# if (platform_name == "D2H" || platform_name == "D2H2" || platform_name == "T1Q3" || platform_name == "T3M" ||  platform_name == "D2J" ) {
#     MLOG_WITH_FILE_MINIEYE = "false"
#     MLOG_WITH_DLT= "true"
# } else {
#     MLOG_WITH_LOGD = "true"
#     MLOG_WITH_FILE_MINIEYE = "true"
# }
if (platform_name == "D4Q" || platform_name == "D4Q2" || platform_name == "D4L" || platform_name == "O1S" || platform_name == "G2Z") {
    MLOG_WITH_LOGD = "true"
    MLOG_WITH_FILE_MINIEYE = "true"
} else {
    MLOG_WITH_FILE_MINIEYE = "false"
    MLOG_WITH_DLT= "true"
}
if (platform_name == "J3") {
    MLOG_WITH_OBFUCATED = "true"
}

config("obfuscation_config") {
    if (MLOG_WITH_OBFUCATED == "true") {
        include_dirs = [
            "utils/LogObfuscator",
        ]

        if (platform_name == "J6E") {
            ldflags = [
                "$sysroot_rebase_path/usr/hobot/lib/aarch64-linux-gnu/libssl.a",
                "$sysroot_rebase_path/usr/hobot/lib/aarch64-linux-gnu/libcrypto.a",
                "-ldl",
            ]
            include_dirs += [
                "$sysroot_rebase_path/usr/hobot/include",
                "$sysroot_rebase_path/usr/hobot/include/aarch64-linux-gnu",
            ]
        }

        if (platform_name == "J3") {
            ldflags = [
                "$sysroot_rebase_path/usr/lib/libssl.a",
                "$sysroot_rebase_path/usr/lib/libcrypto.a",
                "-ldl",
            ]
            include_dirs += [
                "$sysroot_rebase_path/usr/include",
            ]
        }

        defines = [
            "ENABLE_LOG_OBFUSCATION",
            "USE_OPENSSL_CRYPTO",
        ]
    }
}

minieye_group("mlog_group") {
    deps = [
        ":libmlog",
        ":libmlog_static",
        ":mlogcat",
    ]

    if (MLOG_WITH_OBFUCATED == "true") {
        deps += [
            ":mlogdeobf",
        ] 
    }

}


config("mlog_config") {
    include_dirs = [
        "./include",
    ]

    if (target_os == "ubuntu") {
        if (MLOG_WITH_DLT == "true") {
            ldflags = [
                "-ldlt",
            ]
        }
    }

}

minieye_shared_library("libmlog") {

    sources = [
        "src/MiniLog.cpp",
        "src/SocketServer.cpp",
    ]

    public_configs = [ ":mlog_config" ]

    if (MLOG_WITH_DLT == "true") {
        ldflags = [
            "-ldlt",
        ]
        defines = [
            "MLOG_WITH_DLT",
        ]
    }
    if (MLOG_WITH_LOGD == "true") {
        deps = [
            "//middleware/logd/src/liblog:liblogd",
        ] 
        defines = [
            "MLOG_WITH_LOGD",
        ]
    }
    if (MLOG_WITH_FILE_MINIEYE == "true") {
            defines += [
                "MLOG_WITH_FILE_MINIEYE",
            ]
    }
}

static_library("libmlog_static") {

    sources = [
        "src/MiniLog.cpp",
        "src/SocketServer.cpp",
    ]

    public_configs = [ ":mlog_config" ]

    if (MLOG_WITH_DLT == "true") {
        ldflags = [
            "-ldlt",
        ]
        defines = [
            "MLOG_WITH_DLT",
        ]
    }
    if (MLOG_WITH_LOGD == "true") {
        deps = [
            "//middleware/logd/src/liblog:liblogd_static",
        ] 
        defines = [
            "MLOG_WITH_LOGD",
        ]
    }
    if (MLOG_WITH_FILE_MINIEYE == "true") {
            defines += [
                "MLOG_WITH_FILE_MINIEYE",
            ]
    }
}

minieye_executable("mlogcat") {

    if (MLOG_WITH_DLT == "true") {
        sources = [
            "utils/mlogcat_dlt.cpp",
        ]

        ldflags = [
            "-ldlt",
        ]
        defines = [
            "MLOG_WITH_DLT",
        ]
    } else {
        sources = [
            "utils/mlogcat.cpp",
            "src/SocketClient.cpp",
        ]
        include_dirs = [
            "src",
        ]

        if (MLOG_WITH_OBFUCATED == "true") {
            sources += [
                "utils/LogObfuscator/LogObfuscator.cpp",
                "utils/LogObfuscator/MiniAESGCM.cpp",
                "utils/LogObfuscator/MiniRSA.cpp",
                "utils/LogObfuscator/ObfuscatedKeys.cpp",
            ]
            configs += [ ":obfuscation_config" ]
        }
    }
}

if (MLOG_WITH_OBFUCATED == "true") {
    minieye_executable("mlogdeobf") {
        sources = [
            "utils/mlogdeobf.cpp",
            "utils/LogObfuscator/LogObfuscator.cpp",
            "utils/LogObfuscator/MiniAESGCM.cpp",
            "utils/LogObfuscator/MiniRSA.cpp",
            "utils/LogObfuscator/ObfuscatedKeys.cpp",
        ]
        configs += [ ":obfuscation_config" ]
    }
}
