#!/bin/bash

exec 2> /dev/null

ROTATESIZE=2048 #KB
ROTATEGENS_KER=200
ROTATEGENS_USR=200
ROTATEGENS_REMOTE=200
ROTATEGENS_ALL=$((${ROTATEGENS_REMOTE} * 5 + ${ROTATEGENS_KER} + ${ROTATEGENS_USR}))
ROTATESIZE_BYTES=$((${ROTATESIZE} * 1024))
conf_path=/tmp/minieye_log.conf

#soc platform
SOC=J6x
#log dir
SOURCE_DIR=/log/minieye
# source_dir_tmp=$(awk -F '=' '/LOG_DEPLOY_DIR/{print $2}' /etc/runtime_config)
# if [ -n "${source_dir_tmp}" ] && [ "${source_dir_tmp}" != "${SOURCE_DIR}" ]; then
# 	SOURCE_DIR=${source_dir_tmp}
# 	echo "log source dir: ${SOURCE_DIR}"
# 	ls /log &> /dev/null || ln -srf ${SOURCE_DIR} /log
# fi
LOG_SOURCE_DIR=${SOURCE_DIR}
LOG_FILE_NAME=message
LOG_DIR=archive
#kernel log information

#APP log information
MINIEYE_APP_LOG_DIR=${LOG_SOURCE_DIR}/emb
MINIEYE_APP_SAVE_LOG_DIR=${MINIEYE_APP_LOG_DIR}
#APP log information
MINIEYE_ALGO_LOG_DIR=${LOG_SOURCE_DIR}/algo
MINIEYE_ALGO_SAVE_LOG_DIR=${MINIEYE_ALGO_LOG_DIR}
#APP log information
MINIEYE_USER_LOG_DIR=${LOG_SOURCE_DIR}/user
MINIEYE_USER_SAVE_LOG_DIR=${MINIEYE_USER_LOG_DIR}
MINIEYE_BSP_LOG_DIR=${LOG_SOURCE_DIR}/bsp
MINIEYE_BSP_SAVE_LOG_DIR=${MINIEYE_BSP_LOG_DIR}
MINIEYE_ALL_LOG_DIR=${LOG_SOURCE_DIR}/all
MINIEYE_ALL_SAVE_LOG_DIR=${MINIEYE_ALL_LOG_DIR}

#timestamp information for timesync failed
LATEST_LOG_TS=""
TS_COUNT=""
RESET_COUNT=""
FORMARRED_RESET_COUNT=""
RESET_LOG_DIR=${LOG_SOURCE_DIR}
LOG_EXE_FLAG=0
function output()
{
	echo "hobot-log: $1"
	echo "hobot-log: $1" > /dev/kmsg
}
function get_reset_count()
{
	if [ ! -f "/log/reset_count.txt" ] || [ -z "$(cat "/log/reset_count.txt")" ]; then
		RESET_COUNT="0"
	else
		RESET_COUNT=$(cat "/log/reset_count.txt")
	fi
}


#If the timesync failed, change the file time
#$1 the file path
function change_file_time()
{
	if [ -z "${LATEST_LOG_TS}" ]; then
		return 0
	fi

	local year=$(date +%Y%m)
	local year_limit="202410"
	if [ "${year}" -ge "${year_limit}" ]; then
		return 0
	fi

	local file=$1
	local now_ts=$(echo "${TS_COUNT}" | awk '{printf "%d\n", $1+'"${LATEST_LOG_TS}"'}')
	touch -d "$(date '+%F %T' -d @${now_ts})" ${file}
	TS_COUNT=$((${TS_COUNT}+1))
}

#$1 file name with path
function date2ymdhms()
{
	time_type=""
	if [ -f "$1" ]; then
		time_type=$(ls -l --full-time $1 2> /dev/null | awk '{print $6"_"$7}' | awk -F '.' '{print $1}' | tr ':-' '_')
	fi
}

#check log file count
function check_log_cnt()
{
	local dir="$1"
	local log_cnt_max="$2"

	local log_cnt=$(ls -l ${dir} | grep "^-" | wc -l)
	while [ ${log_cnt} -gt ${log_cnt_max} ]; do
		delfile_name=$(ls -ltr ${dir} | grep "^-" -m 1 | awk '{print $9}')
		rm ${dir}/${delfile_name}
		log_cnt=$(ls -l ${dir} | grep "^-" | wc -l)
	done
}

function check_log_cnt_with_keyword()
{
	local dir="$1"
	local keyword="$2"
	local log_cnt_max="$3"

	local log_cnt=$(ls -l ${dir} | grep "^-" | grep ${keyword} | wc -l)
	while [ ${log_cnt} -gt ${log_cnt_max} ]; do
		delfile_name=$(ls -ltr ${dir} | grep "^-" | grep ${keyword} -m 1 | awk '{print $9}')
		rm ${dir}/${delfile_name}
		log_cnt=$(ls -l ${dir} | grep "^-" | grep ${keyword} | wc -l)
	done
}

#check log dir size(MB)
function check_log_dir_size()
{
	local dir="$1"
	local log_dir_size_max="$2"

	local log_dir_size=$(du -s ${dir} | awk '{print $1}')
	while [ ${log_dir_size} -gt ${log_dir_size_max} ]; do
		delfile_name=$(ls -ltr ${dir} | grep "^-" -m 1 | awk '{print $9}')
		#there may be no files in the directory
		if [ -z "${delfile_name}" ]; then
			break
		fi
		rm ${dir}/${delfile_name}
		log_dir_size=$(du -s ${dir} | awk '{print $1}')
	done
}



#$1 origin log dir
#$2 save log dir
#$3 filename prefix
#$4 filename suffix
#$5 maximum log count
function check_first_log()
{
	local origin_dir="$1"
	local save_dir="$2"
	local prefix="$3"
	local suffix="$4"
	local log_cnt_max="$5"
	local save_log_dir file_name time_type_name file_repeat file_inode

	if [ ! -d ${origin_dir} ]; then
		mkdir -p ${origin_dir}
	fi
	save_log_dir="${save_dir}/${LOG_DIR}"
	if [ ! -d ${save_log_dir} ]; then
		mkdir -p ${save_log_dir}
	fi

	for x in $(ls -ptr ${origin_dir} | grep -v / | grep "${LOG_FILE_NAME}"); do
		file_name="${x}"
		if [ -z "${file_name}" ]; then
			continue
		fi
		date2ymdhms ${origin_dir}/${file_name}
		if [ -z "${time_type}" ]; then
			continue
		fi
		time_type_name=${prefix}${time_type}${suffix}

		#Prevents multiple files from being generated in the same second
		#When generate a new file, file(message.7) name replaced(message.6 -> message.7), file time don't match up, slove it
		file_repeat=$(ls ${save_log_dir}/${time_type_name} 2> /dev/null)
		if [ -n "${file_repeat}" ]; then
			date2ymdhms ${file_repeat}
			if [ -e "${save_log_dir}/${prefix}${time_type}${suffix}" ]; then
				file_inode=$(ls -i ${save_log_dir}/${prefix}${time_type}${suffix} 2> /dev/null | awk '{print $1}')
				mv ${file_repeat} ${save_log_dir}/${prefix}${time_type}-${file_inode}${suffix}
			else
				mv ${file_repeat} ${save_log_dir}/${prefix}${time_type}${suffix}
			fi
		fi

		mv ${origin_dir}/${file_name} ${save_log_dir}/${time_type_name}
		change_file_time ${save_log_dir}/${time_type_name}
		check_log_cnt $2/${LOG_DIR} ${log_cnt_max}
		if [ ${file_name} = "${LOG_FILE_NAME}" ]; then
			touch $1/${LOG_FILE_NAME}
		fi
	done
}

#$1 origin log dir
#$2 save log dir
#$3 filename prefix
#$4 filename suffix
#$5 maximum log count
function check_log()
{
	local origin_dir="$1"
	local save_dir="$2"
	local prefix="$3"
	local suffix="$4"
	local log_cnt_max="$5"
	local save_log_dir max_file_size file_name time_type_name file_repeat file_inode

	if [ ! -d ${origin_dir} ]; then
		mkdir -p ${origin_dir}
	fi
	save_log_dir="${save_dir}/${LOG_DIR}"
	if [ ! -d ${save_log_dir} ]; then
		mkdir -p ${save_log_dir}
	fi

	while true; do
		#old file name may change When generate a new file, slove archived failure
		max_file_size=$(ls -lS ${origin_dir}/${LOG_FILE_NAME}.* 2> /dev/null | awk 'NR==1{print $5}')
		if [ -z "${max_file_size}" ] || [ "${max_file_size}" -lt ${ROTATESIZE_BYTES} ]; then
			break
		fi

		for x in $(ls -ptr ${origin_dir} | grep -v / | grep "${LOG_FILE_NAME}"); do
			file_name="${x}"
			if [ -z "${file_name}" ]  || [ ${file_name} = "${LOG_FILE_NAME}" ]; then
				continue
			fi
			date2ymdhms ${origin_dir}/${file_name}
			if [ -z "${time_type}" ]; then
				continue
			fi
			time_type_name=${prefix}${time_type}${suffix}

			#Prevents multiple files from being generated in the same second
			#When generate a new file, file(message.7) name replaced(message.6 -> message.7), file time don't match up, slove it
			file_repeat=$(ls ${save_log_dir}/${time_type_name} 2> /dev/null)
			if [ -n "${file_repeat}" ]; then
				date2ymdhms ${file_repeat}
				if [ -e "${save_log_dir}/${prefix}${time_type}${suffix}" ]; then
					file_inode=$(ls -i ${save_log_dir}/${prefix}${time_type}${suffix} 2> /dev/null | awk '{print $1}')
					mv ${file_repeat} ${save_log_dir}/${prefix}${time_type}-${file_inode}${suffix}
				else
					mv ${file_repeat} ${save_log_dir}/${prefix}${time_type}${suffix}
				fi
			fi

			mv ${origin_dir}/${file_name} ${save_log_dir}/${time_type_name}
			change_file_time ${save_log_dir}/${time_type_name}
			check_log_cnt $2/${LOG_DIR} ${log_cnt_max}
		done
	done
}

function update_time_resetreason()
{
	sed -i "\$ s/2024-01-01-00-00-00/$(date +"%Y-%m-%d-%H-%M-%S")/" ${RESET_LOG_DIR}/reset_reason.txt
}

# function make_log_dir()
# {
# 	mkdir -p ${MINIEYE_APP_LOG_DIR}  ${MINIEYE_APP_SAVE_LOG_DIR}
# 	mkdir -p ${MINIEYE_ALGO_LOG_DIR}  ${MINIEYE_ALGO_SAVE_LOG_DIR}
# 	mkdir -p ${MINIEYE_USER_LOG_DIR}  ${MINIEYE_USER_SAVE_LOG_DIR}
# 	mkdir -p ${MINIEYE_BSP_LOG_DIR}  ${MINIEYE_BSP_SAVE_LOG_DIR}
# 	mkdir -p ${MINIEYE_ALL_LOG_DIR}  ${MINIEYE_ALL_SAVE_LOG_DIR}
# }

function wait_for_timesync()
{
	local year=$(date +%Y%m)
	local year_limit="202410"
	local loop=0
	local loop_max=16

	while [ "${year}" -lt "${year_limit}" ]; do
		timeout 2 /lib/systemd/systemd-timesyncd &
		sleep 1
		if [ "$((++loop))" -ge "${loop_max}" ]; then
			break;
		fi
		year=$(date +%Y%m)
	done

	#Initializes the timestamp when timesync fails
	if [ "${year}" -lt "${year_limit}" ]; then
		local latest_log_file=$(ls -t $(find ${KER_SAVE_LOG_DIR} ${USR_SAVE_LOG_DIR} ${MCU_SAVE_LOG_DIR} ${DSP0_SAVE_LOG_DIR} ${DSP1_SAVE_LOG_DIR} ${BPU_SAVE_LOG_DIR} ${BL31_SAVE_LOG_DIR} ${HSM_SAVE_LOG_DIR} ${UBOOT_LOG} ${PSTORE_LOG} -type f 2> /dev/null) | head -n 1)
		LATEST_LOG_TS=$(date +%s -r "${latest_log_file}")
		TS_COUNT=1
		output "ERR: timesync failed, change the file time for keep the latest log"
	fi
}
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  --start-emb       Start collecting emb logs"
    echo "  --start-algo      Start collecting algorithm logs"
    echo "  --start-user      Start collecting user logs"
    echo "  --start-bsp       Start collecting BSP logs"
    echo "  --start-all       Start collecting all types of logs"
    echo "  --sleep-time=SEC  Set the sleep time between log checks (default: 600 seconds)"
    echo "  --rotate-size=KB  Set the log rotation size in kilobytes (default: 2048 KB)"
    echo "  --help            Show this help message and exit"
}

#for record suspend and resume
echo "$@" | grep -wq "record_reset_reason" && {
	record_reset_reason
	update_time_resetreason
	exit 0
}

is_restart=$(systemctl show-environment | awk -F '=' '/hobotlog_ready/{print $2}')
echo "is_restart $is_restart"
# calculate_rotate_num
# make_log_dir
start_emb=false
start_algo=false
start_user=false
start_bsp=false
start_all=false
sleep_time=600
for arg in "$@"; do
    case $arg in
        --start-emb)
            start_emb=true
            ;;
        --start-algo)
            start_algo=true
            ;;
        --start-user)
            start_user=true
            ;;
        --start-bsp)
            start_bsp=true
            ;;
        --start-all)
            start_all=true
            ;;
        --sleep-time=*)
            sleep_time="${arg#*=}"
            ;;
        --rotate-size=*)
            ROTATESIZE="${arg#*=}"
            ROTATESIZE_BYTES=$((${ROTATESIZE} * 1024))
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown argument: $arg"
            exit 1
            ;;
    esac
done
$start_emb && mkdir -p ${MINIEYE_APP_LOG_DIR}  ${MINIEYE_APP_SAVE_LOG_DIR}
$start_algo && mkdir -p ${MINIEYE_ALGO_LOG_DIR}  ${MINIEYE_ALGO_SAVE_LOG_DIR}
$start_user && mkdir -p ${MINIEYE_USER_LOG_DIR}  ${MINIEYE_USER_SAVE_LOG_DIR}
$start_bsp &&  mkdir -p ${MINIEYE_BSP_LOG_DIR}  ${MINIEYE_BSP_SAVE_LOG_DIR}
$start_all && mkdir -p ${MINIEYE_ALL_LOG_DIR}  ${MINIEYE_ALL_SAVE_LOG_DIR}

get_reset_count
# wait_for_timesync
# 检查上一次启动的log
RESET_COUNT=$((RESET_COUNT-1))
FORMARRED_RESET_COUNT=$(printf "%04d" ${RESET_COUNT})
echo "last count is $FORMARRED_RESET_COUNT"
while true; do
	$start_emb && check_log ${MINIEYE_APP_LOG_DIR}   ${MINIEYE_APP_SAVE_LOG_DIR}   "${SOC}_Emb-${FORMARRED_RESET_COUNT}-" ".Log" ${ROTATEGENS_REMOTE}
	$start_algo && check_log ${MINIEYE_ALGO_LOG_DIR}   ${MINIEYE_ALGO_SAVE_LOG_DIR}   "${SOC}_Algo-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
	$start_user && check_log ${MINIEYE_USER_LOG_DIR}   ${MINIEYE_USER_SAVE_LOG_DIR}   "${SOC}_User-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
	$start_bsp && check_log ${MINIEYE_BSP_LOG_DIR}   ${MINIEYE_USER_SAVE_LOG_DIR}   "${SOC}_Bsp-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
	$start_all && check_log ${MINIEYE_ALL_LOG_DIR}   ${MINIEYE_ALL_SAVE_LOG_DIR}   "${SOC}_All-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}	
	#archive last log
	if [ ${LOG_EXE_FLAG} -eq 0 ]; then
		$start_emb && check_first_log ${MINIEYE_APP_LOG_DIR}   ${MINIEYE_APP_SAVE_LOG_DIR}   "${SOC}_Emb-${FORMARRED_RESET_COUNT}-" ".Log" ${ROTATEGENS_REMOTE}
		$start_algo && check_first_log ${MINIEYE_ALGO_LOG_DIR}   ${MINIEYE_ALGO_SAVE_LOG_DIR}   "${SOC}_Algo-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
		$start_user && check_first_log ${MINIEYE_USER_LOG_DIR}   ${MINIEYE_USER_SAVE_LOG_DIR}   "${SOC}_User-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
		$start_bsp &&  check_first_log ${MINIEYE_BSP_LOG_DIR}   ${MINIEYE_BSP_SAVE_LOG_DIR}   "${SOC}_Bsp-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
		$start_all && check_first_log ${MINIEYE_ALL_LOG_DIR}   ${MINIEYE_ALL_SAVE_LOG_DIR}   "${SOC}_All-${FORMARRED_RESET_COUNT}-"    ".Log" ${ROTATEGENS_REMOTE}
		LOG_EXE_FLAG=1
		# 重新获取count
		get_reset_count
		FORMARRED_RESET_COUNT=$(printf "%04d" ${RESET_COUNT})
		echo "now count is $RESET_COUNT"
		$start_emb && logdcat -b app -s *:I -v time -f ${MINIEYE_APP_LOG_DIR}/${LOG_FILE_NAME} -r${ROTATESIZE} -n ${ROTATEGENS_USR} &
		$start_algo && logdcat -b algo -s *:I -v time -f ${MINIEYE_ALGO_LOG_DIR}/${LOG_FILE_NAME} -r${ROTATESIZE} -n ${ROTATEGENS_USR} &
		$start_user && logdcat -b user -s *:I -v time -f ${MINIEYE_USER_LOG_DIR}/${LOG_FILE_NAME} -r${ROTATESIZE} -n ${ROTATEGENS_USR} &
		$start_bsp && logdcat -b bsp -s *:I -v time -f ${MINIEYE_BSP_LOG_DIR}/${LOG_FILE_NAME} -r${ROTATESIZE} -n ${ROTATEGENS_USR} &
		$start_all && logdcat -s *:I  -v time -f ${MINIEYE_ALL_LOG_DIR}/${LOG_FILE_NAME} -r${ROTATESIZE} -n ${ROTATEGENS_USR} &
	fi
    sleep $sleep_time
done
function kill_logdcat_processes() {
	pkill -f "logdcat"
}
trap kill_logdcat_processes EXIT