ninja_required_version = 1.7.2

rule gn
  command = ../../build/minieye/tools/gn --root=../.. -q --dotfile=../../build/minieye/.gn gen .
  description = Regenerating ninja files

build build.ninja: gn
  generator = 1
  depfile = build.ninja.d

subninja toolchain.ninja

build file_monitor: phony bin/file_monitor
build sample_message: phony bin/sample_message
build sample_threadpool: phony bin/sample_threadpool
build scantree: phony bin/scantree
build cppbase: phony ./libcppbase.so
build message: phony ./libmessage.so
build build/minieye$:all: phony obj/build/minieye/all.stamp
build middleware/system/core/libcppbase$:cppbase: phony ./libcppbase.so
build middleware/system/core/libcppbase/demo$:sample_threadpool: phony bin/sample_threadpool
build middleware/system/core/libmessage$:message: phony ./libmessage.so
build middleware/system/core/libmessage/demo$:sample_message: phony bin/sample_message
build middleware/system/tools/filemonitor$:file_monitor: phony bin/file_monitor
build middleware/system/tools/scantree$:scantree: phony bin/scantree
build middleware/system/tools/scantree: phony bin/scantree

build all: phony $
    obj/build/minieye/all.stamp $
    ./libcppbase.so $
    bin/sample_threadpool $
    ./libmessage.so $
    bin/sample_message $
    bin/file_monitor $
    bin/scantree

default all
