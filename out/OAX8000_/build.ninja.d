build.ninja: ../../build/minieye/.gn ../../build/minieye/BUILD.gn ../../build/minieye/config/BUILD.gn ../../build/minieye/config/BUILDCONFIG.gn ../../build/minieye/minieye.gni ../../build/minieye/os_var.gni ../../build/minieye/templates/copy.gni ../../build/minieye/templates/cxx.gni ../../build/minieye/templates/minieye_templates.gni ../../build/minieye/templates/prebuilt.gni ../../build/minieye/toolchain/BUILD.gn ../../build/minieye/toolchain/gcc.gni ../../middleware/system/core/libcppbase/BUILD.gn ../../middleware/system/core/libcppbase/demo/BUILD.gn ../../middleware/system/core/libmessage/BUILD.gn ../../middleware/system/core/libmessage/demo/BUILD.gn ../../middleware/system/tools/filemonitor/BUILD.gn ../../middleware/system/tools/scantree/BUILD.gn ./args.gn ../../platform/OAX8000/build/platform.json ../../product.json