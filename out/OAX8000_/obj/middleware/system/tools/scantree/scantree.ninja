defines =
include_dirs = -I../../platform/OAX8000/build/appsdk/usr/minieye/include -I../../platform/OAX8000/build/appsdk/usr/ov/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_c = -static -pedantic -Wall -D_LARGEFILE64_SOURCE -D_FILE_OFFSET_BITS=64 -fno-strict-aliasing -Wno-extra
target_output_name = scantree

build obj/middleware/system/tools/scantree/scantree.scantree.o: cc ../../middleware/system/tools/scantree/scantree.c
build obj/middleware/system/tools/scantree/scantree.list.o: cc ../../middleware/system/tools/scantree/list.c
build obj/middleware/system/tools/scantree/scantree.hash.o: cc ../../middleware/system/tools/scantree/hash.c
build obj/middleware/system/tools/scantree/scantree.color.o: cc ../../middleware/system/tools/scantree/color.c
build obj/middleware/system/tools/scantree/scantree.file.o: cc ../../middleware/system/tools/scantree/file.c
build obj/middleware/system/tools/scantree/scantree.filter.o: cc ../../middleware/system/tools/scantree/filter.c
build obj/middleware/system/tools/scantree/scantree.info.o: cc ../../middleware/system/tools/scantree/info.c
build obj/middleware/system/tools/scantree/scantree.unix.o: cc ../../middleware/system/tools/scantree/unix.c
build obj/middleware/system/tools/scantree/scantree.xml.o: cc ../../middleware/system/tools/scantree/xml.c
build obj/middleware/system/tools/scantree/scantree.json.o: cc ../../middleware/system/tools/scantree/json.c
build obj/middleware/system/tools/scantree/scantree.html.o: cc ../../middleware/system/tools/scantree/html.c
build obj/middleware/system/tools/scantree/scantree.strverscmp.o: cc ../../middleware/system/tools/scantree/strverscmp.c
build obj/middleware/system/tools/scantree/scantree.md5sum.o: cc ../../middleware/system/tools/scantree/md5sum.c

build bin/scantree: link obj/middleware/system/tools/scantree/scantree.scantree.o obj/middleware/system/tools/scantree/scantree.list.o obj/middleware/system/tools/scantree/scantree.hash.o obj/middleware/system/tools/scantree/scantree.color.o obj/middleware/system/tools/scantree/scantree.file.o obj/middleware/system/tools/scantree/scantree.filter.o obj/middleware/system/tools/scantree/scantree.info.o obj/middleware/system/tools/scantree/scantree.unix.o obj/middleware/system/tools/scantree/scantree.xml.o obj/middleware/system/tools/scantree/scantree.json.o obj/middleware/system/tools/scantree/scantree.html.o obj/middleware/system/tools/scantree/scantree.strverscmp.o obj/middleware/system/tools/scantree/scantree.md5sum.o
  ldflags = -s -L/home/<USER>/mine/out/OAX8000_ -Wl,-rpath-link=/home/<USER>/mine/out/OAX8000_ -lpthread -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib
  libs =
  output_extension = 
  output_dir = bin
