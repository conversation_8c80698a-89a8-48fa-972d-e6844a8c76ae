defines =
include_dirs = -I../../platform/OAX8000/build/appsdk/usr/minieye/include -I../../platform/OAX8000/build/appsdk/usr/ov/include -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = sample_message

build obj/middleware/system/core/libmessage/demo/sample_message.sample_message.o: cxx ../../middleware/system/core/libmessage/demo/sample_message.cpp

build bin/sample_message: link obj/middleware/system/core/libmessage/demo/sample_message.sample_message.o ./libmessage.so
  ldflags = -L/home/<USER>/mine/out/OAX8000_ -Wl,-rpath-link=/home/<USER>/mine/out/OAX8000_ -lpthread -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib
  libs =
  output_extension = 
  output_dir = bin
