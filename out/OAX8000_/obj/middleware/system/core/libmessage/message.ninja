defines =
include_dirs = -I../../platform/OAX8000/build/appsdk/usr/minieye/include -I../../platform/OAX8000/build/appsdk/usr/ov/include -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIC
cflags_cc = -Wno-unused-variable -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIC
target_output_name = libmessage

build obj/middleware/system/core/libmessage/src/libmessage.AHandler.o: cxx ../../middleware/system/core/libmessage/src/AHandler.cpp
build obj/middleware/system/core/libmessage/src/libmessage.ALooper.o: cxx ../../middleware/system/core/libmessage/src/ALooper.cpp
build obj/middleware/system/core/libmessage/src/libmessage.AMessage.o: cxx ../../middleware/system/core/libmessage/src/AMessage.cpp
build obj/middleware/system/core/libmessage/src/libmessage.AString.o: cxx ../../middleware/system/core/libmessage/src/AString.cpp
build obj/middleware/system/core/libmessage/src/libmessage.AThread.o: cxx ../../middleware/system/core/libmessage/src/AThread.cpp

build ./libmessage.so: solink obj/middleware/system/core/libmessage/src/libmessage.AHandler.o obj/middleware/system/core/libmessage/src/libmessage.ALooper.o obj/middleware/system/core/libmessage/src/libmessage.AMessage.o obj/middleware/system/core/libmessage/src/libmessage.AString.o obj/middleware/system/core/libmessage/src/libmessage.AThread.o
  ldflags = -L/home/<USER>/mine/out/OAX8000_ -Wl,-rpath-link=/home/<USER>/mine/out/OAX8000_ -lpthread -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib -Wl,-rpath-link=/home/<USER>/mine/platform/OAX8000/build/appsdk/usr/ov/lib
  libs =
  output_extension = .so
  output_dir = .
