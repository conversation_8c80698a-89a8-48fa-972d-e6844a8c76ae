defines =
include_dirs = -I../../middleware/daemon/src/server -I../../middleware/daemon/src/server/util -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -fpermissive -std=c++11 -Wno-implicit-fallthrough -Wno-unused-function -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = daemon

build obj/middleware/daemon/src/server/daemon.parser.o: cxx ../../middleware/daemon/src/server/parser.cpp
build obj/middleware/daemon/src/server/daemon.import_parser.o: cxx ../../middleware/daemon/src/server/import_parser.cpp
build obj/middleware/daemon/src/server/daemon.init.o: cxx ../../middleware/daemon/src/server/init.cpp
build obj/middleware/daemon/src/server/daemon.action.o: cxx ../../middleware/daemon/src/server/action.cpp
build obj/middleware/daemon/src/server/daemon.property_service.o: cxx ../../middleware/daemon/src/server/property_service.cpp
build obj/middleware/daemon/src/server/daemon.init_parser.o: cxx ../../middleware/daemon/src/server/init_parser.cpp
build obj/middleware/daemon/src/server/daemon.builtins.o: cxx ../../middleware/daemon/src/server/builtins.cpp
build obj/middleware/daemon/src/server/daemon.ueventd.o: cxx ../../middleware/daemon/src/server/ueventd.cpp
build obj/middleware/daemon/src/server/daemon.devices.o: cxx ../../middleware/daemon/src/server/devices.cpp
build obj/middleware/daemon/src/server/daemon.service.o: cxx ../../middleware/daemon/src/server/service.cpp
build obj/middleware/daemon/src/server/daemon.signal_handler.o: cxx ../../middleware/daemon/src/server/signal_handler.cpp
build obj/middleware/daemon/src/server/util/daemon.iosched_policy.o: cxx ../../middleware/daemon/src/server/util/iosched_policy.cpp
build obj/middleware/daemon/src/server/util/daemon.uevent.o: cxx ../../middleware/daemon/src/server/util/uevent.cpp
build obj/middleware/daemon/src/server/util/daemon.mstrings.o: cxx ../../middleware/daemon/src/server/util/mstrings.cpp
build obj/middleware/daemon/src/server/util/daemon.util.o: cxx ../../middleware/daemon/src/server/util/util.cpp
build obj/middleware/daemon/src/server/util/daemon.log.o: cxx ../../middleware/daemon/src/server/util/log.cpp
build obj/middleware/daemon/src/server/util/daemon.stringprintf.o: cxx ../../middleware/daemon/src/server/util/stringprintf.cpp
build obj/middleware/daemon/src/server/util/daemon.nn_pub.o: cxx ../../middleware/daemon/src/server/util/nn_pub.cpp

build bin/daemon: link obj/middleware/daemon/src/server/daemon.parser.o obj/middleware/daemon/src/server/daemon.import_parser.o obj/middleware/daemon/src/server/daemon.init.o obj/middleware/daemon/src/server/daemon.action.o obj/middleware/daemon/src/server/daemon.property_service.o obj/middleware/daemon/src/server/daemon.init_parser.o obj/middleware/daemon/src/server/daemon.builtins.o obj/middleware/daemon/src/server/daemon.ueventd.o obj/middleware/daemon/src/server/daemon.devices.o obj/middleware/daemon/src/server/daemon.service.o obj/middleware/daemon/src/server/daemon.signal_handler.o obj/middleware/daemon/src/server/util/daemon.iosched_policy.o obj/middleware/daemon/src/server/util/daemon.uevent.o obj/middleware/daemon/src/server/util/daemon.mstrings.o obj/middleware/daemon/src/server/util/daemon.util.o obj/middleware/daemon/src/server/util/daemon.log.o obj/middleware/daemon/src/server/util/daemon.stringprintf.o obj/middleware/daemon/src/server/util/daemon.nn_pub.o ./libproperty.so
  ldflags = -lnanomsg -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
