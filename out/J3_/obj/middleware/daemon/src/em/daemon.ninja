defines =
include_dirs = -I../../middleware/daemon/include -I../../middleware/mlog/include -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
target_output_name = libdaemon

build obj/middleware/daemon/src/em/libdaemon.ExecutionManager.o: cxx ../../middleware/daemon/src/em/ExecutionManager.cpp

build ./libdaemon.so: solink obj/middleware/daemon/src/em/libdaemon.ExecutionManager.o ./libmlog.so ./libproperty.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
