defines =
include_dirs = -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = stoprc

build obj/middleware/daemon/utils/stoprc/stoprc.stoprc.o: cxx ../../middleware/daemon/utils/stoprc/stoprc.cpp

build bin/stoprc: link obj/middleware/daemon/utils/stoprc/stoprc.stoprc.o ./libproperty.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
