defines =
include_dirs = -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = sample_systemprop

build obj/middleware/persistency/sample/sample_systemprop.sample_systemprop.o: cxx ../../middleware/persistency/sample/sample_systemprop.cpp

build bin/sample_systemprop: link obj/middleware/persistency/sample/sample_systemprop.sample_systemprop.o ./libpersistency.so ./libpersistency_common.so ./libmlog.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
