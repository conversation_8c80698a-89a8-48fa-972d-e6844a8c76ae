defines =
include_dirs = -I../../middleware/persistency/src -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = classtest_persistency

build obj/middleware/persistency/test/classtest/classtest_persistency.main.o: cxx ../../middleware/persistency/test/classtest/main.cpp
build obj/middleware/persistency/test/classtest/classtest_persistency.Test_ParamsHandler.o: cxx ../../middleware/persistency/test/classtest/Test_ParamsHandler.cpp
build obj/middleware/persistency/test/classtest/classtest_persistency.Test_ReqRsp.o: cxx ../../middleware/persistency/test/classtest/Test_ReqRsp.cpp
build obj/middleware/persistency/test/classtest/classtest_persistency.Test_PubSubMessage.o: cxx ../../middleware/persistency/test/classtest/Test_PubSubMessage.cpp
build obj/middleware/persistency/test/classtest/classtest_persistency.Test_Message.o: cxx ../../middleware/persistency/test/classtest/Test_Message.cpp
build obj/middleware/persistency/test/classtest/classtest_persistency.Test_PubSub.o: cxx ../../middleware/persistency/test/classtest/Test_PubSub.cpp

build bin/classtest_persistency: link obj/middleware/persistency/test/classtest/classtest_persistency.main.o obj/middleware/persistency/test/classtest/classtest_persistency.Test_ParamsHandler.o obj/middleware/persistency/test/classtest/classtest_persistency.Test_ReqRsp.o obj/middleware/persistency/test/classtest/classtest_persistency.Test_PubSubMessage.o obj/middleware/persistency/test/classtest/classtest_persistency.Test_Message.o obj/middleware/persistency/test/classtest/classtest_persistency.Test_PubSub.o ./libpersistency_common.so ./libmlog.so
  ldflags = -lgtest -lgtest_main -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
