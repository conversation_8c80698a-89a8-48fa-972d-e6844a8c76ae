defines = -DMLOG_WITH_DLT
include_dirs = -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = libmlog_static

build obj/middleware/mlog/src/libmlog_static.MiniLog.o: cxx ../../middleware/mlog/src/MiniLog.cpp
build obj/middleware/mlog/src/libmlog_static.SocketServer.o: cxx ../../middleware/mlog/src/SocketServer.cpp

build ./libmlog_static.a: alink obj/middleware/mlog/src/libmlog_static.MiniLog.o obj/middleware/mlog/src/libmlog_static.SocketServer.o
  arflags =
  output_extension = .a
  output_dir = .
