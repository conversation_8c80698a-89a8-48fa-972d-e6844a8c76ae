defines = -DMLOG_WITH_DLT
include_dirs =
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = mlogcat

build obj/middleware/mlog/utils/mlogcat.mlogcat_dlt.o: cxx ../../middleware/mlog/utils/mlogcat_dlt.cpp

build bin/mlogcat: link obj/middleware/mlog/utils/mlogcat.mlogcat_dlt.o
  ldflags = -ldlt -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
