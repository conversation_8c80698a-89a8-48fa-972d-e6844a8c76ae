defines = -DMLOG_WITH_DLT
include_dirs = -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
target_output_name = libmlog

build obj/middleware/mlog/src/libmlog.MiniLog.o: cxx ../../middleware/mlog/src/MiniLog.cpp
build obj/middleware/mlog/src/libmlog.SocketServer.o: cxx ../../middleware/mlog/src/SocketServer.cpp

build ./libmlog.so: solink obj/middleware/mlog/src/libmlog.MiniLog.o obj/middleware/mlog/src/libmlog.SocketServer.o
  ldflags = -ldlt -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = .so
  output_dir = .
