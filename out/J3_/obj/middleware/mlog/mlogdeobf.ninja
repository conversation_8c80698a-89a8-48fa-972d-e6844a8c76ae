defines = -DENABLE_LOG_OBFUSCATION -DUSE_OPENSSL_CRYPTO
include_dirs = -I../../middleware/mlog/utils/LogObfuscator -I../../platform/J3/build/sysroots/usr/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = mlogdeobf

build obj/middleware/mlog/utils/mlogdeobf.mlogdeobf.o: cxx ../../middleware/mlog/utils/mlogdeobf.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.LogObfuscator.o: cxx ../../middleware/mlog/utils/LogObfuscator/LogObfuscator.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniAESGCM.o: cxx ../../middleware/mlog/utils/LogObfuscator/MiniAESGCM.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniRSA.o: cxx ../../middleware/mlog/utils/LogObfuscator/MiniRSA.cpp
build obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.ObfuscatedKeys.o: cxx ../../middleware/mlog/utils/LogObfuscator/ObfuscatedKeys.cpp

build bin/mlogdeobf: link obj/middleware/mlog/utils/mlogdeobf.mlogdeobf.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.LogObfuscator.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniAESGCM.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.MiniRSA.o obj/middleware/mlog/utils/LogObfuscator/mlogdeobf.ObfuscatedKeys.o
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots /home/<USER>/mine/platform/J3/build/sysroots/usr/lib/libssl.a /home/<USER>/mine/platform/J3/build/sysroots/usr/lib/libcrypto.a -ldl
  libs =
  output_extension = 
  output_dir = bin
