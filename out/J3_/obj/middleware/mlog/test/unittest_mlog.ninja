defines =
include_dirs = -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = unittest_mlog

build obj/middleware/mlog/test/unittest/unittest_mlog.main.o: cxx ../../middleware/mlog/test/unittest/main.cpp
build obj/middleware/mlog/test/unittest/unittest_mlog.Test_MiniLog.o: cxx ../../middleware/mlog/test/unittest/Test_MiniLog.cpp

build bin/unittest_mlog: link obj/middleware/mlog/test/unittest/unittest_mlog.main.o obj/middleware/mlog/test/unittest/unittest_mlog.Test_MiniLog.o ./libmlog.so
  ldflags = -lgtest -lgtest_main -ldlt -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
