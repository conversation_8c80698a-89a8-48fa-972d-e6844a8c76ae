defines =
include_dirs = -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = mlogsend

build obj/middleware/mlog/sample/mlogsend.mlogsend.o: cxx ../../middleware/mlog/sample/mlogsend.cpp

build bin/mlogsend: link obj/middleware/mlog/sample/mlogsend.mlogsend.o ./libmlog.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
