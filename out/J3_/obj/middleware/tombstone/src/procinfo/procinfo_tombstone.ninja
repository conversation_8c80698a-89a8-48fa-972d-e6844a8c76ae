defines =
include_dirs = -I../../middleware/tombstone/src/procinfo -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -fPIC -std=c++11 -pthread -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = libprocinfo_tombstone

build obj/middleware/tombstone/src/procinfo/libprocinfo_tombstone.process.o: cxx ../../middleware/tombstone/src/procinfo/process.cpp

build ./libprocinfo_tombstone.a: alink obj/middleware/tombstone/src/procinfo/libprocinfo_tombstone.process.o || ./libbase_tombstone.a
  arflags =
  output_extension = .a
  output_dir = .
