defines =
include_dirs = -I../../middleware/tombstone/src/backtrace -I../../middleware/tombstone/src/backtrace/include -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/procinfo -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/unwindstack -I../../middleware/tombstone/src/unwindstack/include
cflags = -fPIC -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_c =
cflags_cc = -fPIC -std=c++17 -pthread -Wno-deprecated-declarations -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = libbacktrace_tombstone

build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.Backtrace.o: cxx ../../middleware/tombstone/src/backtrace/Backtrace.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktraceCurrent.o: cxx ../../middleware/tombstone/src/backtrace/BacktraceCurrent.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktracePtrace.o: cxx ../../middleware/tombstone/src/backtrace/BacktracePtrace.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.thread_utils.o: cc ../../middleware/tombstone/src/backtrace/thread_utils.c
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.ThreadEntry.o: cxx ../../middleware/tombstone/src/backtrace/ThreadEntry.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.UnwindStack.o: cxx ../../middleware/tombstone/src/backtrace/UnwindStack.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.UnwindStackMap.o: cxx ../../middleware/tombstone/src/backtrace/UnwindStackMap.cpp
build obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktraceMap.o: cxx ../../middleware/tombstone/src/backtrace/BacktraceMap.cpp

build ./libbacktrace_tombstone.a: alink obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.Backtrace.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktraceCurrent.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktracePtrace.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.thread_utils.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.ThreadEntry.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.UnwindStack.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.UnwindStackMap.o obj/middleware/tombstone/src/backtrace/libbacktrace_tombstone.BacktraceMap.o || ./libbase_tombstone.a ./libprocinfo_tombstone.a ./libunwindstack_tombstone.a ./libdemangle_tombstone.a ./liblzma_tombstone.a
  arflags =
  output_extension = .a
  output_dir = .
