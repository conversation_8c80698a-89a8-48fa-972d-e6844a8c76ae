defines =
include_dirs = -I../../middleware/tombstone/src/debuggerd/client/include -I../../middleware/tombstone/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -std=c++11 -pthread -g -fPIC -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = test

build obj/middleware/tombstone/src/debuggerd/test/test.test2.o: cxx ../../middleware/tombstone/src/debuggerd/test/test2.cpp

build bin/test: link obj/middleware/tombstone/src/debuggerd/test/test.test2.o ./libtombstone_client.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
