defines =
include_dirs = -I../../middleware/tombstone/src/debuggerd -I../../middleware/tombstone/src/libcutils/include -I../../middleware/tombstone/src/backtrace/include -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/procinfo -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -std=c++17 -pthread -Wno-unused-function -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-address -Wno-maybe-uninitialized -Wno-deprecated-declarations -Wno-range-loop-construct -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = tombstone

build obj/middleware/tombstone/src/debuggerd/tombstone.backtrace.o: cxx ../../middleware/tombstone/src/debuggerd/backtrace.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.debuggerd.o: cxx ../../middleware/tombstone/src/debuggerd/debuggerd.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.elf_utils.o: cxx ../../middleware/tombstone/src/debuggerd/elf_utils.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.getevent.o: cxx ../../middleware/tombstone/src/debuggerd/getevent.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.signal_sender.o: cxx ../../middleware/tombstone/src/debuggerd/signal_sender.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.tombstone.o: cxx ../../middleware/tombstone/src/debuggerd/tombstone.cpp
build obj/middleware/tombstone/src/debuggerd/tombstone.utility.o: cxx ../../middleware/tombstone/src/debuggerd/utility.cpp
build obj/middleware/tombstone/src/debuggerd/arm64/tombstone.machine.o: cxx ../../middleware/tombstone/src/debuggerd/arm64/machine.cpp

build bin/tombstone: link obj/middleware/tombstone/src/debuggerd/tombstone.backtrace.o obj/middleware/tombstone/src/debuggerd/tombstone.debuggerd.o obj/middleware/tombstone/src/debuggerd/tombstone.elf_utils.o obj/middleware/tombstone/src/debuggerd/tombstone.getevent.o obj/middleware/tombstone/src/debuggerd/tombstone.signal_sender.o obj/middleware/tombstone/src/debuggerd/tombstone.tombstone.o obj/middleware/tombstone/src/debuggerd/tombstone.utility.o obj/middleware/tombstone/src/debuggerd/arm64/tombstone.machine.o ./libcutils_tombstone.a ./libbacktrace_tombstone.a ./libbase_tombstone.a ./libprocinfo_tombstone.a ./libunwindstack_tombstone.a ./liblzma_tombstone.a ./libdemangle_tombstone.a
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
