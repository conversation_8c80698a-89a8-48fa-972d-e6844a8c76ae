defines =
include_dirs = -I../../middleware/tombstone/src/unwindstack/lzma -I../../middleware/tombstone/src/unwindstack/lzma/C
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_c = -fPIC -D_7ZIP_ST -Wno-misleading-indentation
target_output_name = liblzma_tombstone

build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zAlloc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zAlloc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zArcIn.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zArcIn.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zBuf2.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zBuf2.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zBuf.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zBuf.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zCrc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zCrc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zCrcOpt.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zCrcOpt.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zDec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zDec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zFile.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zFile.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zStream.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/7zStream.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Aes.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Aes.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.AesOpt.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/AesOpt.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Alloc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Alloc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bcj2.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Bcj2.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bra86.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Bra86.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bra.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Bra.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.BraIA64.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/BraIA64.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.CpuArch.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/CpuArch.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Delta.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Delta.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzFind.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/LzFind.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma2Dec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Lzma2Dec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma2Enc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Lzma2Enc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma86Dec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Lzma86Dec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma86Enc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Lzma86Enc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaDec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/LzmaDec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaEnc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/LzmaEnc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaLib.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/LzmaLib.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Ppmd7.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7Dec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Ppmd7Dec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7Enc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Ppmd7Enc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Sha256.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Sha256.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Sort.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Sort.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Xz.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/Xz.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzCrc64.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/XzCrc64.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzCrc64Opt.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/XzCrc64Opt.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzDec.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/XzDec.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzEnc.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/XzEnc.c
build obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzIn.o: cc ../../middleware/tombstone/src/unwindstack/lzma/C/XzIn.c

build ./liblzma_tombstone.a: alink obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zAlloc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zArcIn.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zBuf2.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zBuf.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zCrc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zCrcOpt.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zDec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zFile.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.7zStream.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Aes.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.AesOpt.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Alloc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bcj2.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bra86.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Bra.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.BraIA64.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.CpuArch.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Delta.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzFind.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma2Dec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma2Enc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma86Dec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Lzma86Enc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaDec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaEnc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.LzmaLib.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7Dec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Ppmd7Enc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Sha256.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Sort.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.Xz.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzCrc64.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzCrc64Opt.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzDec.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzEnc.o obj/middleware/tombstone/src/unwindstack/lzma/C/liblzma_tombstone.XzIn.o
  arflags =
  output_extension = .a
  output_dir = .
