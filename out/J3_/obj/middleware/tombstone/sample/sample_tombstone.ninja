defines =
include_dirs = -I../../middleware/tombstone/src/debuggerd/client/include -I../../middleware/tombstone/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = sample_tombstone

build obj/middleware/tombstone/sample/sample_tombstone.sample_tombstone.o: cxx ../../middleware/tombstone/sample/sample_tombstone.cpp

build bin/sample_tombstone: link obj/middleware/tombstone/sample/sample_tombstone.sample_tombstone.o ./libtombstone_client.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
