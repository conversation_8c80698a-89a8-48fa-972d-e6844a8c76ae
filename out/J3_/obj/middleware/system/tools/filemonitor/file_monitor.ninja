defines =
include_dirs =
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = file_monitor

build obj/middleware/system/tools/filemonitor/file_monitor.main.o: cxx ../../middleware/system/tools/filemonitor/main.cpp
build obj/middleware/system/tools/filemonitor/file_monitor.FileMonitor.o: cxx ../../middleware/system/tools/filemonitor/FileMonitor.cpp

build bin/file_monitor: link obj/middleware/system/tools/filemonitor/file_monitor.main.o obj/middleware/system/tools/filemonitor/file_monitor.FileMonitor.o
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
