defines =
include_dirs = -I../../middleware/system/tools/system_res_monitor/src/inc -I../../middleware/system/tools/system_res_monitor/src/inc
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_c =
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = system_res_monitor

build obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.system_res_monitor.o: cxx ../../middleware/system/tools/system_res_monitor/src/system_res_monitor.cpp || obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp
build obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.cJSON.o: cc ../../middleware/system/tools/system_res_monitor/src/cJSON.c || obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp
build obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.upload_info.o: cxx ../../middleware/system/tools/system_res_monitor/src/upload_info.cpp || obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp
build obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.resource.pb.o: cxx ../../middleware/system/tools/system_res_monitor/src/resource.pb.cc || obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp

build bin/system_res_monitor: link obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.system_res_monitor.o obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.cJSON.o obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.upload_info.o obj/middleware/system/tools/system_res_monitor/src/system_res_monitor.resource.pb.o | libs/libprotobuf_arm64_for_system_res_monitor.a || obj/middleware/system/tools/system_res_monitor/res_protobuf.stamp
  ldflags = -lpthread -lstdc++ -lflow -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs = libs/libprotobuf_arm64_for_system_res_monitor.a
  output_extension = 
  output_dir = bin
