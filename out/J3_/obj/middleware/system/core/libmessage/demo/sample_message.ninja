defines =
include_dirs = -I../../middleware/system/core/libmessage/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIE
target_output_name = sample_message

build obj/middleware/system/core/libmessage/demo/sample_message.sample_message.o: cxx ../../middleware/system/core/libmessage/demo/sample_message.cpp

build bin/sample_message: link obj/middleware/system/core/libmessage/demo/sample_message.sample_message.o ./libmessage.so
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = 
  output_dir = bin
