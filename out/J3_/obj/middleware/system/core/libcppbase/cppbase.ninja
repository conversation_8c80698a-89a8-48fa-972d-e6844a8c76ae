defines =
include_dirs = -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J3/build/sysroots -fPIC
target_output_name = libcppbase

build obj/middleware/system/core/libcppbase/bufferqueue/libcppbase.CppBaseBufferQueue.o: cxx ../../middleware/system/core/libcppbase/bufferqueue/CppBaseBufferQueue.cpp
build obj/middleware/system/core/libcppbase/threadpool/libcppbase.CppBaseThreadPool.o: cxx ../../middleware/system/core/libcppbase/threadpool/CppBaseThreadPool.cpp
build obj/middleware/system/core/libcppbase/md5/libcppbase.CppBaseMd5.o: cxx ../../middleware/system/core/libcppbase/md5/CppBaseMd5.cpp
build obj/middleware/system/core/libcppbase/crc/libcppbase.CppBaseCrc.o: cxx ../../middleware/system/core/libcppbase/crc/CppBaseCrc.cpp
build obj/middleware/system/core/libcppbase/time/libcppbase.CppBaseTime.o: cxx ../../middleware/system/core/libcppbase/time/CppBaseTime.cpp
build obj/middleware/system/core/libcppbase/file/libcppbase.CppBaseFileUtil.o: cxx ../../middleware/system/core/libcppbase/file/CppBaseFileUtil.cpp
build obj/middleware/system/core/libcppbase/string/libcppbase.CppBaseString.o: cxx ../../middleware/system/core/libcppbase/string/CppBaseString.cpp

build ./libcppbase.so: solink obj/middleware/system/core/libcppbase/bufferqueue/libcppbase.CppBaseBufferQueue.o obj/middleware/system/core/libcppbase/threadpool/libcppbase.CppBaseThreadPool.o obj/middleware/system/core/libcppbase/md5/libcppbase.CppBaseMd5.o obj/middleware/system/core/libcppbase/crc/libcppbase.CppBaseCrc.o obj/middleware/system/core/libcppbase/time/libcppbase.CppBaseTime.o obj/middleware/system/core/libcppbase/file/libcppbase.CppBaseFileUtil.o obj/middleware/system/core/libcppbase/string/libcppbase.CppBaseString.o
  ldflags = -L/home/<USER>/mine/out/J3_ -Wl,-rpath-link=/home/<USER>/mine/out/J3_ -lpthread --sysroot=/home/<USER>/mine/platform/J3/build/sysroots
  libs =
  output_extension = .so
  output_dir = .
