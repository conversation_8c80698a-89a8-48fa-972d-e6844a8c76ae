build.ninja: ../../build/minieye/.gn ../../build/minieye/BUILD.gn ../../build/minieye/config/BUILD.gn ../../build/minieye/config/BUILDCONFIG.gn ../../build/minieye/minieye.gni ../../build/minieye/os_var.gni ../../build/minieye/templates/copy.gni ../../build/minieye/templates/cxx.gni ../../build/minieye/templates/minieye_templates.gni ../../build/minieye/templates/prebuilt.gni ../../build/minieye/toolchain/BUILD.gn ../../build/minieye/toolchain/gcc.gni ../../middleware/daemon/BUILD.gn ../../middleware/daemon/sample/BUILD.gn ../../middleware/daemon/src/em/BUILD.gn ../../middleware/daemon/src/property/BUILD.gn ../../middleware/daemon/src/server/BUILD.gn ../../middleware/daemon/utils/BUILD.gn ../../middleware/mlog/BUILD.gn ../../middleware/mlog/sample/BUILD.gn ../../middleware/mlog/test/BUILD.gn ../../middleware/persistency/BUILD.gn ../../middleware/persistency/sample/BUILD.gn ../../middleware/persistency/test/BUILD.gn ../../middleware/persistency/utils/BUILD.gn ../../middleware/system/core/libcppbase/BUILD.gn ../../middleware/system/core/libcppbase/demo/BUILD.gn ../../middleware/system/core/libmessage/BUILD.gn ../../middleware/system/core/libmessage/demo/BUILD.gn ../../middleware/system/tools/filemonitor/BUILD.gn ../../middleware/system/tools/scantree/BUILD.gn ../../middleware/system/tools/system_res_monitor/BUILD.gn ../../middleware/tombstone/BUILD.gn ../../middleware/tombstone/sample/BUILD.gn ../../middleware/tombstone/src/backtrace/BUILD.gn ../../middleware/tombstone/src/backtrace/demangle/BUILD.gn ../../middleware/tombstone/src/base/BUILD.gn ../../middleware/tombstone/src/debuggerd/BUILD.gn ../../middleware/tombstone/src/libcutils/BUILD.gn ../../middleware/tombstone/src/procinfo/BUILD.gn ../../middleware/tombstone/src/unwindstack/BUILD.gn ../../middleware/tombstone/src/unwindstack/lzma/BUILD.gn ../../middleware/tombstone/utils/BUILD.gn ./args.gn ../../platform/J3/build/platform.json ../../product.json