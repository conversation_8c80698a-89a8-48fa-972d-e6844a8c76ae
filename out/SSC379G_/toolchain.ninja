rule cc
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-gcc -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags} ${cflags_c} -c ${in} -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule cxx
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-g++ -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags_cc} -c ${in} -o ${out}
  description = CXX ${out}
  depfile = ${out}.d
  deps = gcc
rule asm
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-gcc ${defines} ${include_dirs} ${asmflags} ${in} -c -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule alink
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-ar cr ${out} @"${out}.rsp"
  description = AR ${out}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule solink
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-g++ -shared -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} && /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-strip ${output_dir}/${target_output_name}${output_extension}
  description = SOLINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule link
  command = /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-g++ -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} && /home/<USER>/mine/platform/SSC379G/build/gcc-11.1.0-20210608-sigmastar-glibc-x86_64_arm-linux-gnueabihf/bin/arm-linux-gnueabihf-strip ${output_dir}/${target_output_name}${output_extension}
  description = LINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${output_dir}/${target_output_name}${output_extension}.rsp
  rspfile_content = ${in}
rule stamp
  command = touch ${out}
  description = STAMP ${out}
rule copy
  command = ln -f ${in} ${out} 2>/dev/null || (rm -rf ${out} && cp -af ${in} ${out})
  description = COPY ${in} ${out}

build obj/build/minieye/all.stamp: stamp obj/middleware/logd/logd_group.stamp bin/scantree bin/file_monitor bin/sample_threadpool bin/sample_message
build obj/middleware/logd/logd_group.stamp: stamp bin/logdcat bin/logd ./liblogd_static.a bin/sample_logd
subninja obj/middleware/logd/sample/sample_logd.ninja
subninja obj/middleware/logd/src/base/base_logd.ninja
subninja obj/middleware/logd/src/libcutils/libcutils_logd.ninja
subninja obj/middleware/logd/src/liblog/liblogd.ninja
subninja obj/middleware/logd/src/liblog/liblogd_static.ninja
subninja obj/middleware/logd/src/libpackagelistparser/libpackagelistparser_logd.ninja
subninja obj/middleware/logd/src/libsysutils/libsysutils_logd.ninja
subninja obj/middleware/logd/src/logcat/logdcat.ninja
subninja obj/middleware/logd/src/logd/logd.ninja
subninja obj/middleware/logd/src/pcre/libpcre_logd.ninja
subninja obj/middleware/logd/src/pcre/libpcrecpp_logd.ninja
subninja obj/middleware/system/core/libcppbase/cppbase.ninja
subninja obj/middleware/system/core/libcppbase/demo/sample_threadpool.ninja
subninja obj/middleware/system/core/libmessage/message.ninja
subninja obj/middleware/system/core/libmessage/demo/sample_message.ninja
subninja obj/middleware/system/tools/filemonitor/file_monitor.ninja
subninja obj/middleware/system/tools/scantree/scantree.ninja
