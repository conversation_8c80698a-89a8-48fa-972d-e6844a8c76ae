defines =
include_dirs = -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include -I../../middleware/system/core/libcppbase -I../../middleware/system/core/libcppbase/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIC
target_output_name = libcppbase

build obj/middleware/system/core/libcppbase/bufferqueue/libcppbase.CppBaseBufferQueue.o: cxx ../../middleware/system/core/libcppbase/bufferqueue/CppBaseBufferQueue.cpp
build obj/middleware/system/core/libcppbase/threadpool/libcppbase.CppBaseThreadPool.o: cxx ../../middleware/system/core/libcppbase/threadpool/CppBaseThreadPool.cpp
build obj/middleware/system/core/libcppbase/md5/libcppbase.CppBaseMd5.o: cxx ../../middleware/system/core/libcppbase/md5/CppBaseMd5.cpp
build obj/middleware/system/core/libcppbase/crc/libcppbase.CppBaseCrc.o: cxx ../../middleware/system/core/libcppbase/crc/CppBaseCrc.cpp
build obj/middleware/system/core/libcppbase/time/libcppbase.CppBaseTime.o: cxx ../../middleware/system/core/libcppbase/time/CppBaseTime.cpp
build obj/middleware/system/core/libcppbase/file/libcppbase.CppBaseFileUtil.o: cxx ../../middleware/system/core/libcppbase/file/CppBaseFileUtil.cpp
build obj/middleware/system/core/libcppbase/string/libcppbase.CppBaseString.o: cxx ../../middleware/system/core/libcppbase/string/CppBaseString.cpp

build ./libcppbase.so: solink obj/middleware/system/core/libcppbase/bufferqueue/libcppbase.CppBaseBufferQueue.o obj/middleware/system/core/libcppbase/threadpool/libcppbase.CppBaseThreadPool.o obj/middleware/system/core/libcppbase/md5/libcppbase.CppBaseMd5.o obj/middleware/system/core/libcppbase/crc/libcppbase.CppBaseCrc.o obj/middleware/system/core/libcppbase/time/libcppbase.CppBaseTime.o obj/middleware/system/core/libcppbase/file/libcppbase.CppBaseFileUtil.o obj/middleware/system/core/libcppbase/string/libcppbase.CppBaseString.o
  ldflags = -L/home/<USER>/mine/out/SSC379G_ -Wl,-rpath-link=/home/<USER>/mine/out/SSC379G_ -lpthread -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib
  libs =
  output_extension = .so
  output_dir = .
