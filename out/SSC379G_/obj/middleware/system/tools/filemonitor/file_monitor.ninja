defines =
include_dirs = -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = file_monitor

build obj/middleware/system/tools/filemonitor/file_monitor.main.o: cxx ../../middleware/system/tools/filemonitor/main.cpp
build obj/middleware/system/tools/filemonitor/file_monitor.FileMonitor.o: cxx ../../middleware/system/tools/filemonitor/FileMonitor.cpp

build bin/file_monitor: link obj/middleware/system/tools/filemonitor/file_monitor.main.o obj/middleware/system/tools/filemonitor/file_monitor.FileMonitor.o
  ldflags = -L/home/<USER>/mine/out/SSC379G_ -Wl,-rpath-link=/home/<USER>/mine/out/SSC379G_ -lpthread -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib
  libs =
  output_extension = 
  output_dir = bin
