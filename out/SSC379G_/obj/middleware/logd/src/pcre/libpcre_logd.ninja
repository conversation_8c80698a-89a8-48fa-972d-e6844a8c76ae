defines =
include_dirs = -I../../middleware/logd/src/pcre -I../../middleware/logd/src/pcre/dist -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_c = -DHAVE_CONFIG_H -Wno-unused-parameter -Wno-unused-variable -Wno-misleading-indentation -Wno-array-bounds -Wno-implicit-fallthrough -Wno-maybe-uninitialized
target_output_name = libpcre_logd

build obj/middleware/logd/src/pcre/libpcre_logd.pcre_chartables.o: cc ../../middleware/logd/src/pcre/pcre_chartables.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_byte_order.o: cc ../../middleware/logd/src/pcre/dist/pcre_byte_order.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_compile.o: cc ../../middleware/logd/src/pcre/dist/pcre_compile.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_config.o: cc ../../middleware/logd/src/pcre/dist/pcre_config.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_dfa_exec.o: cc ../../middleware/logd/src/pcre/dist/pcre_dfa_exec.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_exec.o: cc ../../middleware/logd/src/pcre/dist/pcre_exec.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_fullinfo.o: cc ../../middleware/logd/src/pcre/dist/pcre_fullinfo.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_get.o: cc ../../middleware/logd/src/pcre/dist/pcre_get.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_globals.o: cc ../../middleware/logd/src/pcre/dist/pcre_globals.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_jit_compile.o: cc ../../middleware/logd/src/pcre/dist/pcre_jit_compile.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_maketables.o: cc ../../middleware/logd/src/pcre/dist/pcre_maketables.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_newline.o: cc ../../middleware/logd/src/pcre/dist/pcre_newline.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_ord2utf8.o: cc ../../middleware/logd/src/pcre/dist/pcre_ord2utf8.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_refcount.o: cc ../../middleware/logd/src/pcre/dist/pcre_refcount.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_string_utils.o: cc ../../middleware/logd/src/pcre/dist/pcre_string_utils.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_study.o: cc ../../middleware/logd/src/pcre/dist/pcre_study.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_tables.o: cc ../../middleware/logd/src/pcre/dist/pcre_tables.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_ucd.o: cc ../../middleware/logd/src/pcre/dist/pcre_ucd.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_valid_utf8.o: cc ../../middleware/logd/src/pcre/dist/pcre_valid_utf8.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_version.o: cc ../../middleware/logd/src/pcre/dist/pcre_version.c
build obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_xclass.o: cc ../../middleware/logd/src/pcre/dist/pcre_xclass.c

build ./libpcre_logd.a: alink obj/middleware/logd/src/pcre/libpcre_logd.pcre_chartables.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_byte_order.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_compile.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_config.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_dfa_exec.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_exec.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_fullinfo.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_get.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_globals.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_jit_compile.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_maketables.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_newline.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_ord2utf8.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_refcount.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_string_utils.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_study.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_tables.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_ucd.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_valid_utf8.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_version.o obj/middleware/logd/src/pcre/dist/libpcre_logd.pcre_xclass.o
  arflags =
  output_extension = .a
  output_dir = .
