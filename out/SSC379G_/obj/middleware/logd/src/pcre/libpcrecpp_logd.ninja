defines =
include_dirs = -I../../middleware/logd/src/pcre -I../../middleware/logd/src/pcre/dist -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -std=c++11 -Wno-misleading-indentation -Wno-unused-variable -Wno-maybe-uninitialized -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = libpcrecpp_logd

build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcrecpp.o: cxx ../../middleware/logd/src/pcre/dist/pcrecpp.cc
build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_scanner.o: cxx ../../middleware/logd/src/pcre/dist/pcre_scanner.cc
build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_stringpiece.o: cxx ../../middleware/logd/src/pcre/dist/pcre_stringpiece.cc

build ./libpcrecpp_logd.a: alink obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcrecpp.o obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_scanner.o obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_stringpiece.o || ./libpcre_logd.a
  arflags =
  output_extension = .a
  output_dir = .
