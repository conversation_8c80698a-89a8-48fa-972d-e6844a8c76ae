defines =
include_dirs = -I../../middleware/logd/src/libpackagelistparser/include -I../../middleware/logd/src/include -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_c = -std=c11 -Wno-int-conversion -Wno-implicit-function-declaration -Wno-sign-compare
target_output_name = libpackagelistparser_logd

build obj/middleware/logd/src/libpackagelistparser/libpackagelistparser_logd.packagelistparser.o: cc ../../middleware/logd/src/libpackagelistparser/packagelistparser.c

build ./libpackagelistparser_logd.a: alink obj/middleware/logd/src/libpackagelistparser/libpackagelistparser_logd.packagelistparser.o
  arflags =
  output_extension = .a
  output_dir = .
