defines =
include_dirs = -I../../middleware/logd/src/base/include -I../../middleware/logd/src/include -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-comment -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = libbase_logd

build obj/middleware/logd/src/base/libbase_logd.file.o: cxx ../../middleware/logd/src/base/file.cpp
build obj/middleware/logd/src/base/libbase_logd.logging.o: cxx ../../middleware/logd/src/base/logging.cpp
build obj/middleware/logd/src/base/libbase_logd.parsenetaddress.o: cxx ../../middleware/logd/src/base/parsenetaddress.cpp
build obj/middleware/logd/src/base/libbase_logd.stringprintf.o: cxx ../../middleware/logd/src/base/stringprintf.cpp
build obj/middleware/logd/src/base/libbase_logd.strings.o: cxx ../../middleware/logd/src/base/strings.cpp
build obj/middleware/logd/src/base/libbase_logd.errors_unix.o: cxx ../../middleware/logd/src/base/errors_unix.cpp

build ./libbase_logd.a: alink obj/middleware/logd/src/base/libbase_logd.file.o obj/middleware/logd/src/base/libbase_logd.logging.o obj/middleware/logd/src/base/libbase_logd.parsenetaddress.o obj/middleware/logd/src/base/libbase_logd.stringprintf.o obj/middleware/logd/src/base/libbase_logd.strings.o obj/middleware/logd/src/base/libbase_logd.errors_unix.o
  arflags =
  output_extension = .a
  output_dir = .
