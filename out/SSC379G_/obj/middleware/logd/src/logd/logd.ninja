defines =
include_dirs = -I../../middleware/logd/src/logd -I../../middleware/logd/src/logd/include -I../../middleware/logd/src/include -I../../middleware/logd/src/base/include -I../../middleware/logd/src/libpackagelistparser/include -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_c = -std=c11 -Wno-strict-aliasing -Wno-comment
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-comment -Wno-misleading-indentation -Wno-unused-but-set-variable -Wno-unused-result -Wno-comment -Wno-sign-compare -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = logd

build obj/middleware/logd/src/logd/logd.main.o: cxx ../../middleware/logd/src/logd/main.cpp
build obj/middleware/logd/src/logd/logd.LogCommand.o: cxx ../../middleware/logd/src/logd/LogCommand.cpp
build obj/middleware/logd/src/logd/logd.CommandListener.o: cxx ../../middleware/logd/src/logd/CommandListener.cpp
build obj/middleware/logd/src/logd/logd.LogListener.o: cxx ../../middleware/logd/src/logd/LogListener.cpp
build obj/middleware/logd/src/logd/logd.LogReader.o: cxx ../../middleware/logd/src/logd/LogReader.cpp
build obj/middleware/logd/src/logd/logd.FlushCommand.o: cxx ../../middleware/logd/src/logd/FlushCommand.cpp
build obj/middleware/logd/src/logd/logd.LogBuffer.o: cxx ../../middleware/logd/src/logd/LogBuffer.cpp
build obj/middleware/logd/src/logd/logd.LogBufferElement.o: cxx ../../middleware/logd/src/logd/LogBufferElement.cpp
build obj/middleware/logd/src/logd/logd.LogTimes.o: cxx ../../middleware/logd/src/logd/LogTimes.cpp
build obj/middleware/logd/src/logd/logd.LogStatistics.o: cxx ../../middleware/logd/src/logd/LogStatistics.cpp
build obj/middleware/logd/src/logd/logd.LogWhiteBlackList.o: cxx ../../middleware/logd/src/logd/LogWhiteBlackList.cpp
build obj/middleware/logd/src/logd/logd.libaudit.o: cc ../../middleware/logd/src/logd/libaudit.c
build obj/middleware/logd/src/logd/logd.LogAudit.o: cxx ../../middleware/logd/src/logd/LogAudit.cpp
build obj/middleware/logd/src/logd/logd.LogKlog.o: cxx ../../middleware/logd/src/logd/LogKlog.cpp
build obj/middleware/logd/src/logd/configure/logd.configure.o: cxx ../../middleware/logd/src/logd/configure/configure.cpp

build bin/logd: link obj/middleware/logd/src/logd/logd.main.o obj/middleware/logd/src/logd/logd.LogCommand.o obj/middleware/logd/src/logd/logd.CommandListener.o obj/middleware/logd/src/logd/logd.LogListener.o obj/middleware/logd/src/logd/logd.LogReader.o obj/middleware/logd/src/logd/logd.FlushCommand.o obj/middleware/logd/src/logd/logd.LogBuffer.o obj/middleware/logd/src/logd/logd.LogBufferElement.o obj/middleware/logd/src/logd/logd.LogTimes.o obj/middleware/logd/src/logd/logd.LogStatistics.o obj/middleware/logd/src/logd/logd.LogWhiteBlackList.o obj/middleware/logd/src/logd/logd.libaudit.o obj/middleware/logd/src/logd/logd.LogAudit.o obj/middleware/logd/src/logd/logd.LogKlog.o obj/middleware/logd/src/logd/configure/logd.configure.o ./libsysutils_logd.a ./liblogd.so ./libcutils_logd.a ./libbase_logd.a ./libpackagelistparser_logd.a
  ldflags = -L/home/<USER>/mine/out/SSC379G_ -Wl,-rpath-link=/home/<USER>/mine/out/SSC379G_ -lpthread -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib
  libs =
  output_extension = 
  output_dir = bin
