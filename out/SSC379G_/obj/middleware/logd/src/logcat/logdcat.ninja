defines =
include_dirs = -I../../middleware/logd/src/include -I../../middleware/logd/src/base/include -I../../middleware/logd/src/pcre/dist -I../../middleware/logd/src/pcre -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-unused-variable -Wno-comment -Wno-unused-result -Wno-format -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = logdcat

build obj/middleware/logd/src/logcat/logdcat.logcat.o: cxx ../../middleware/logd/src/logcat/logcat.cpp

build bin/logdcat: link obj/middleware/logd/src/logcat/logdcat.logcat.o ./liblogd.so ./libcutils_logd.a ./libbase_logd.a ./libpcrecpp_logd.a ./libpcre_logd.a
  ldflags = -L/home/<USER>/mine/out/SSC379G_ -Wl,-rpath-link=/home/<USER>/mine/out/SSC379G_ -lpthread -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib
  libs =
  output_extension = 
  output_dir = bin
