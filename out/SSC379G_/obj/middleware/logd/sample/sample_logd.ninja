defines =
include_dirs = -I../../platform/SSC379G/build/appsdk/usr/minieye/include -I../../platform/SSC379G/build/appsdk/usr/sstar/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_32 -O3 -DNDEBUG -fPIE
target_output_name = sample_logd

build obj/middleware/logd/sample/sample_logd.sample_logd.o: cxx ../../middleware/logd/sample/sample_logd.cpp

build bin/sample_logd: link obj/middleware/logd/sample/sample_logd.sample_logd.o ./liblogd.so
  ldflags = -L/home/<USER>/mine/out/SSC379G_ -Wl,-rpath-link=/home/<USER>/mine/out/SSC379G_ -lpthread -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/minieye/lib -L/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib -Wl,-rpath-link=/home/<USER>/mine/platform/SSC379G/build/appsdk/usr/sstar/lib
  libs =
  output_extension = 
  output_dir = bin
