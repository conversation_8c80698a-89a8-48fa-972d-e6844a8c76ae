ninja_required_version = 1.7.2

rule gn
  command = ../../build/minieye/tools/gn --root=../.. -q --dotfile=../../build/minieye/.gn gen .
  description = Regenerating ninja files

build build.ninja: gn
  generator = 1
  depfile = build.ninja.d

subninja toolchain.ninja

build file_monitor: phony bin/file_monitor
build logd: phony bin/logd
build logdcat: phony bin/logdcat
build sample_logd: phony bin/sample_logd
build sample_message: phony bin/sample_message
build sample_threadpool: phony bin/sample_threadpool
build scantree: phony bin/scantree
build base_logd: phony ./libbase_logd.a
build cppbase: phony ./libcppbase.so
build libcutils_logd: phony ./libcutils_logd.a
build liblogd: phony ./liblogd.so
build liblogd_static: phony ./liblogd_static.a
build libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build libpcre_logd: phony ./libpcre_logd.a
build libpcrecpp_logd: phony ./libpcrecpp_logd.a
build libsysutils_logd: phony ./libsysutils_logd.a
build logd_group: phony obj/middleware/logd/logd_group.stamp
build message: phony ./libmessage.so
build build/minieye$:all: phony obj/build/minieye/all.stamp
build middleware/logd$:logd_group: phony obj/middleware/logd/logd_group.stamp
build middleware/logd/sample$:sample_logd: phony bin/sample_logd
build middleware/logd/src/base$:base_logd: phony ./libbase_logd.a
build middleware/logd/src/libcutils$:libcutils_logd: phony ./libcutils_logd.a
build middleware/logd/src/liblog$:liblogd: phony ./liblogd.so
build middleware/logd/src/liblog$:liblogd_static: phony ./liblogd_static.a
build middleware/logd/src/libpackagelistparser$:libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build middleware/logd/src/libsysutils$:libsysutils_logd: phony ./libsysutils_logd.a
build middleware/logd/src/logcat$:logdcat: phony bin/logdcat
build middleware/logd/src/logd$:logd: phony bin/logd
build middleware/logd/src/logd: phony bin/logd
build middleware/logd/src/pcre$:libpcre_logd: phony ./libpcre_logd.a
build middleware/logd/src/pcre$:libpcrecpp_logd: phony ./libpcrecpp_logd.a
build middleware/system/core/libcppbase$:cppbase: phony ./libcppbase.so
build middleware/system/core/libcppbase/demo$:sample_threadpool: phony bin/sample_threadpool
build middleware/system/core/libmessage$:message: phony ./libmessage.so
build middleware/system/core/libmessage/demo$:sample_message: phony bin/sample_message
build middleware/system/tools/filemonitor$:file_monitor: phony bin/file_monitor
build middleware/system/tools/scantree$:scantree: phony bin/scantree
build middleware/system/tools/scantree: phony bin/scantree

build all: phony $
    obj/build/minieye/all.stamp $
    obj/middleware/logd/logd_group.stamp $
    bin/sample_logd $
    ./libbase_logd.a $
    ./libcutils_logd.a $
    ./liblogd.so $
    ./liblogd_static.a $
    ./libpackagelistparser_logd.a $
    ./libsysutils_logd.a $
    bin/logdcat $
    bin/logd $
    ./libpcre_logd.a $
    ./libpcrecpp_logd.a $
    ./libcppbase.so $
    bin/sample_threadpool $
    ./libmessage.so $
    bin/sample_message $
    bin/file_monitor $
    bin/scantree

default all
