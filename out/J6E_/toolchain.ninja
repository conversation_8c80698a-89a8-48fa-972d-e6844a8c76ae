rule cc
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags} ${cflags_c} -c ${in} -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule cxx
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -MMD -MF ${out}.d ${defines} ${include_dirs} ${cflags_cc} -c ${in} -o ${out}
  description = CXX ${out}
  depfile = ${out}.d
  deps = gcc
rule asm
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc ${defines} ${include_dirs} ${asmflags} ${in} -c -o ${out}
  description = cross compiler ${out}
  depfile = ${out}.d
  deps = gcc
rule alink
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-ar cr ${out} @"${out}.rsp"
  description = AR ${out}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule solink
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -shared -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} && /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip ${output_dir}/${target_output_name}${output_extension}
  description = SOLINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${out}.rsp
  rspfile_content = ${in}
rule link
  command = /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ -Wl,--start-group ${in} ${libs} -Wl,--end-group -o ${output_dir}/${target_output_name}${output_extension} ${ldflags} && /home/<USER>/mine/platform/J6E/build/arm-gnu-toolchain-12.2.rel1-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-strip ${output_dir}/${target_output_name}${output_extension}
  description = LINK ${output_dir}/${target_output_name}${output_extension}
  rspfile = ${output_dir}/${target_output_name}${output_extension}.rsp
  rspfile_content = ${in}
rule stamp
  command = touch ${out}
  description = STAMP ${out}
rule copy
  command = ln -f ${in} ${out} 2>/dev/null || (rm -rf ${out} && cp -af ${in} ${out})
  description = COPY ${in} ${out}

build obj/build/minieye/all.stamp: stamp bin/scantree bin/file_monitor bin/sample_threadpool bin/sample_message obj/middleware/mlog/mlog_group.stamp obj/middleware/mlog/test/mlog_test.stamp obj/middleware/mlog/sample/mlog_sample.stamp obj/middleware/daemon/daemon_group.stamp obj/middleware/daemon/test/daemon_test.stamp obj/middleware/persistency/persistency_group.stamp obj/middleware/persistency/test/persistency_test.stamp obj/middleware/persistency/sample/persistency_sample.stamp obj/middleware/logd/logd_group.stamp obj/middleware/dumpsys/dumpsys_group.stamp obj/middleware/tombstone/tombstone_group.stamp
subninja obj/middleware/communication/nanomsg/PairTest.ninja
subninja obj/middleware/communication/nanomsg/PubSubTest.ninja
subninja obj/middleware/communication/nanomsg/ReqRepTest.ninja
subninja obj/middleware/communication/nanomsg/SurveyTest.ninja
subninja obj/middleware/communication/nanomsg/libnnmsg.ninja
build obj/middleware/communication/nanomsg/library_nanomsg_group.stamp: stamp ./libnnmsg.so bin/ReqRepTest bin/PubSubTest bin/PairTest
build obj/middleware/daemon/daemon_group.stamp: stamp bin/daemon ./libdaemon.so obj/middleware/daemon/utils/utils.stamp obj/middleware/daemon/sample/daemon_sample.stamp
build obj/middleware/daemon/sample/daemon_sample.stamp: stamp bin/sample_em
subninja obj/middleware/daemon/sample/sample_em.ninja
subninja obj/middleware/daemon/src/em/daemon.ninja
subninja obj/middleware/daemon/src/property/property.ninja
subninja obj/middleware/daemon/src/server/daemon.ninja
build obj/middleware/daemon/test/daemon_test.stamp: stamp bin/unittest_daemon
subninja obj/middleware/daemon/test/stress_test_daemon.ninja
subninja obj/middleware/daemon/test/unittest_daemon.ninja
subninja obj/middleware/daemon/test/unittest_daemon_sh.ninja
subninja obj/middleware/daemon/utils/dumprc.ninja
subninja obj/middleware/daemon/utils/getsystemprop.ninja
subninja obj/middleware/daemon/utils/setsystemprop.ninja
subninja obj/middleware/daemon/utils/startrc.ninja
subninja obj/middleware/daemon/utils/stoprc.ninja
build obj/middleware/daemon/utils/utils.stamp: stamp bin/getsystemprop bin/setsystemprop bin/startrc bin/stoprc
subninja obj/middleware/dumpsys/dumpsys.ninja
build obj/middleware/dumpsys/dumpsys_group.stamp: stamp bin/dumpsys bin/sample_dumpsys ./libdumpsys_interface.so
subninja obj/middleware/dumpsys/libdumpsys_interface.ninja
subninja obj/middleware/dumpsys/sample_dumpsys.ninja
build obj/middleware/logd/logd_group.stamp: stamp bin/logdcat bin/logd ./liblogd_static.a bin/sample_logd
subninja obj/middleware/logd/sample/sample_logd.ninja
subninja obj/middleware/logd/src/base/base_logd.ninja
subninja obj/middleware/logd/src/libcutils/libcutils_logd.ninja
subninja obj/middleware/logd/src/liblog/liblogd.ninja
subninja obj/middleware/logd/src/liblog/liblogd_static.ninja
subninja obj/middleware/logd/src/libpackagelistparser/libpackagelistparser_logd.ninja
subninja obj/middleware/logd/src/libsysutils/libsysutils_logd.ninja
subninja obj/middleware/logd/src/logcat/logdcat.ninja
subninja obj/middleware/logd/src/logd/logd.ninja
subninja obj/middleware/logd/src/pcre/libpcre_logd.ninja
subninja obj/middleware/logd/src/pcre/libpcrecpp_logd.ninja
subninja obj/middleware/mlog/libmlog.ninja
subninja obj/middleware/mlog/libmlog_static.ninja
build obj/middleware/mlog/mlog_group.stamp: stamp ./libmlog.so ./libmlog_static.a bin/mlogcat
subninja obj/middleware/mlog/mlogcat.ninja
build obj/middleware/mlog/sample/mlog_sample.stamp: stamp bin/sample_mlog bin/mlogsend
subninja obj/middleware/mlog/sample/mlogsend.ninja
subninja obj/middleware/mlog/sample/sample_mlog.ninja
build obj/middleware/mlog/test/mlog_test.stamp: stamp bin/unittest_mlog bin/performance
subninja obj/middleware/mlog/test/performance.ninja
subninja obj/middleware/mlog/test/unittest_mlog.ninja
subninja obj/middleware/persistency/libpersistency.ninja
subninja obj/middleware/persistency/libpersistency_common.ninja
subninja obj/middleware/persistency/persistency.ninja
build obj/middleware/persistency/persistency_group.stamp: stamp bin/persistency obj/middleware/persistency/utils/utils.stamp
build obj/middleware/persistency/sample/persistency_sample.stamp: stamp bin/sample_atomicfile bin/sample_shareconfig
subninja obj/middleware/persistency/sample/sample_atomicfile.ninja
subninja obj/middleware/persistency/sample/sample_shareconfig.ninja
subninja obj/middleware/persistency/sample/sample_systemprop.ninja
subninja obj/middleware/persistency/test/classtest_persistency.ninja
build obj/middleware/persistency/test/persistency_test.stamp: stamp bin/unittest_persistency bin/classtest_persistency bin/shareconfigcrc bin/shareconfigstress
subninja obj/middleware/persistency/test/shareconfigcrc.ninja
subninja obj/middleware/persistency/test/shareconfigstress.ninja
subninja obj/middleware/persistency/test/unittest_persistency.ninja
subninja obj/middleware/persistency/utils/getshareconfig.ninja
subninja obj/middleware/persistency/utils/setshareconfig.ninja
build obj/middleware/persistency/utils/utils.stamp: stamp bin/setshareconfig bin/getshareconfig
subninja obj/middleware/system/core/libcppbase/cppbase.ninja
subninja obj/middleware/system/core/libcppbase/demo/sample_threadpool.ninja
subninja obj/middleware/system/core/libmessage/message.ninja
subninja obj/middleware/system/core/libmessage/demo/sample_message.ninja
subninja obj/middleware/system/tools/filemonitor/file_monitor.ninja
subninja obj/middleware/system/tools/scantree/scantree.ninja
build obj/middleware/tombstone/tombstone_group.stamp: stamp bin/backtrace_tool bin/tombstone bin/test ./libtombstone_client.so bin/sample_tombstone
subninja obj/middleware/tombstone/sample/sample_tombstone.ninja
subninja obj/middleware/tombstone/src/backtrace/backtrace_tombstone.ninja
subninja obj/middleware/tombstone/src/backtrace/demangle/demangle_tombstone.ninja
subninja obj/middleware/tombstone/src/base/base_tombstone.ninja
subninja obj/middleware/tombstone/src/debuggerd/test.ninja
subninja obj/middleware/tombstone/src/debuggerd/tombstone.ninja
subninja obj/middleware/tombstone/src/debuggerd/tombstone_client.ninja
subninja obj/middleware/tombstone/src/libcutils/libcutils_tombstone.ninja
subninja obj/middleware/tombstone/src/procinfo/procinfo_tombstone.ninja
subninja obj/middleware/tombstone/src/unwindstack/unwindstack_tombstone.ninja
subninja obj/middleware/tombstone/src/unwindstack/lzma/lzma_tombstone.ninja
subninja obj/middleware/tombstone/utils/backtrace_tool.ninja
