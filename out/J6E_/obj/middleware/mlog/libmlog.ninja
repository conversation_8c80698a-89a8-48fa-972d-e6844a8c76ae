defines = -DMLOG_WITH_DLT
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libmlog

build obj/middleware/mlog/src/libmlog.MiniLog.o: cxx ../../middleware/mlog/src/MiniLog.cpp
build obj/middleware/mlog/src/libmlog.SocketServer.o: cxx ../../middleware/mlog/src/SocketServer.cpp

build ./libmlog.so: solink obj/middleware/mlog/src/libmlog.MiniLog.o obj/middleware/mlog/src/libmlog.SocketServer.o
  ldflags = -ldlt -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
