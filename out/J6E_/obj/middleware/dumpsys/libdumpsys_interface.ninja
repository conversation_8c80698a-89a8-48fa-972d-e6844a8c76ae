defines =
include_dirs = -I../../middleware/dumpsys/src/common -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/dumpsys/include/dump_interface -I../../middleware/communication/nanomsg/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libdumpsys_interface

build obj/middleware/dumpsys/src/dump_interface/libdumpsys_interface.IDumpSys.o: cxx ../../middleware/dumpsys/src/dump_interface/IDumpSys.cpp
build obj/middleware/dumpsys/src/dump_interface/libdumpsys_interface.DumpManagerImpl.o: cxx ../../middleware/dumpsys/src/dump_interface/DumpManagerImpl.cpp
build obj/middleware/dumpsys/src/common/libdumpsys_interface.Cmd.o: cxx ../../middleware/dumpsys/src/common/Cmd.cpp

build ./libdumpsys_interface.so: solink obj/middleware/dumpsys/src/dump_interface/libdumpsys_interface.IDumpSys.o obj/middleware/dumpsys/src/dump_interface/libdumpsys_interface.DumpManagerImpl.o obj/middleware/dumpsys/src/common/libdumpsys_interface.Cmd.o ./libnnmsg.so
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
