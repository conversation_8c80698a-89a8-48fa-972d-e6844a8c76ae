defines =
include_dirs = -I../../middleware/dumpsys/include/dumpsys -I../../middleware/dumpsys/src/common -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/communication/nanomsg/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = dumpsys

build obj/middleware/dumpsys/src/dumpsys/dumpsys.main.o: cxx ../../middleware/dumpsys/src/dumpsys/main.cpp
build obj/middleware/dumpsys/src/dumpsys/dumpsys.DumpRequest.o: cxx ../../middleware/dumpsys/src/dumpsys/DumpRequest.cpp
build obj/middleware/dumpsys/src/common/dumpsys.Cmd.o: cxx ../../middleware/dumpsys/src/common/Cmd.cpp

build bin/dumpsys: link obj/middleware/dumpsys/src/dumpsys/dumpsys.main.o obj/middleware/dumpsys/src/dumpsys/dumpsys.DumpRequest.o obj/middleware/dumpsys/src/common/dumpsys.Cmd.o ./libnnmsg.so
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
