defines =
include_dirs = -I../../middleware/tombstone/src/unwindstack/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/tombstone/src/unwindstack -I../../middleware/tombstone/src/unwindstack/include -I../../middleware/tombstone/src/unwindstack/lzma -I../../middleware/tombstone/src/unwindstack/lzma/C -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/procinfo -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -fPIC -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fPIC -std=c++17 -pthread -Wno-sign-compare -Wno-missing-field-initializers -Wno-implicit-fallthrough -Wno-maybe-uninitialized -Wno-unused-variable -Wno-deprecated-declarations -Wno-range-loop-construct -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libunwindstack_tombstone

build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ArmExidx.o: cxx ../../middleware/tombstone/src/unwindstack/ArmExidx.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfCfa.o: cxx ../../middleware/tombstone/src/unwindstack/DwarfCfa.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfEhFrameWithHdr.o: cxx ../../middleware/tombstone/src/unwindstack/DwarfEhFrameWithHdr.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfMemory.o: cxx ../../middleware/tombstone/src/unwindstack/DwarfMemory.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfOp.o: cxx ../../middleware/tombstone/src/unwindstack/DwarfOp.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfSection.o: cxx ../../middleware/tombstone/src/unwindstack/DwarfSection.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Elf.o: cxx ../../middleware/tombstone/src/unwindstack/Elf.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ElfInterface.o: cxx ../../middleware/tombstone/src/unwindstack/ElfInterface.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ElfInterfaceArm.o: cxx ../../middleware/tombstone/src/unwindstack/ElfInterfaceArm.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.JitDebug.o: cxx ../../middleware/tombstone/src/unwindstack/JitDebug.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Log.o: cxx ../../middleware/tombstone/src/unwindstack/Log.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.MapInfo.o: cxx ../../middleware/tombstone/src/unwindstack/MapInfo.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Maps.o: cxx ../../middleware/tombstone/src/unwindstack/Maps.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Memory.o: cxx ../../middleware/tombstone/src/unwindstack/Memory.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Regs.o: cxx ../../middleware/tombstone/src/unwindstack/Regs.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsArm.o: cxx ../../middleware/tombstone/src/unwindstack/RegsArm.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsArm64.o: cxx ../../middleware/tombstone/src/unwindstack/RegsArm64.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsX86.o: cxx ../../middleware/tombstone/src/unwindstack/RegsX86.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsX86_64.o: cxx ../../middleware/tombstone/src/unwindstack/RegsX86_64.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsMips.o: cxx ../../middleware/tombstone/src/unwindstack/RegsMips.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsMips64.o: cxx ../../middleware/tombstone/src/unwindstack/RegsMips64.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Unwinder.o: cxx ../../middleware/tombstone/src/unwindstack/Unwinder.cpp
build obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Symbols.o: cxx ../../middleware/tombstone/src/unwindstack/Symbols.cpp

build ./libunwindstack_tombstone.a: alink obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ArmExidx.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfCfa.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfEhFrameWithHdr.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfMemory.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfOp.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.DwarfSection.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Elf.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ElfInterface.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.ElfInterfaceArm.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.JitDebug.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Log.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.MapInfo.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Maps.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Memory.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Regs.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsArm.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsArm64.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsX86.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsX86_64.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsMips.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.RegsMips64.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Unwinder.o obj/middleware/tombstone/src/unwindstack/libunwindstack_tombstone.Symbols.o || ./liblzma_tombstone.a ./libbase_tombstone.a ./libprocinfo_tombstone.a
  arflags =
  output_extension = .a
  output_dir = .
