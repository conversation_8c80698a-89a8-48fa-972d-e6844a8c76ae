defines =
include_dirs = -I../../middleware/tombstone/src/libcutils/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_c =
cflags_cc = -std=c++17 -pthread -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libcutils_tombstone

build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.config_utils.o: cc ../../middleware/tombstone/src/libcutils/config_utils.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.hashmap.o: cc ../../middleware/tombstone/src/libcutils/hashmap.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.iosched_policy.o: cc ../../middleware/tombstone/src/libcutils/iosched_policy.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.load_file.o: cc ../../middleware/tombstone/src/libcutils/load_file.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.native_handle.o: cc ../../middleware/tombstone/src/libcutils/native_handle.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.open_memstream.o: cc ../../middleware/tombstone/src/libcutils/open_memstream.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.process_name.o: cc ../../middleware/tombstone/src/libcutils/process_name.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.record_stream.o: cc ../../middleware/tombstone/src/libcutils/record_stream.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.sockets.o: cxx ../../middleware/tombstone/src/libcutils/sockets.cpp
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.strlcpy.o: cc ../../middleware/tombstone/src/libcutils/strlcpy.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.threads.o: cc ../../middleware/tombstone/src/libcutils/threads.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.multiuser.o: cc ../../middleware/tombstone/src/libcutils/multiuser.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_inaddr_any_server_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_inaddr_any_server_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_local_client_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_local_client_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_local_server_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_local_server_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_loopback_client_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_loopback_client_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_loopback_server_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_loopback_server_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_network_client_unix.o: cc ../../middleware/tombstone/src/libcutils/socket_network_client_unix.c
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.sockets_unix.o: cxx ../../middleware/tombstone/src/libcutils/sockets_unix.cpp
build obj/middleware/tombstone/src/libcutils/libcutils_tombstone.debugger.o: cc ../../middleware/tombstone/src/libcutils/debugger.c

build ./libcutils_tombstone.a: alink obj/middleware/tombstone/src/libcutils/libcutils_tombstone.config_utils.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.hashmap.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.iosched_policy.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.load_file.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.native_handle.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.open_memstream.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.process_name.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.record_stream.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.sockets.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.strlcpy.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.threads.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.multiuser.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_inaddr_any_server_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_local_client_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_local_server_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_loopback_client_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_loopback_server_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.socket_network_client_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.sockets_unix.o obj/middleware/tombstone/src/libcutils/libcutils_tombstone.debugger.o
  arflags =
  output_extension = .a
  output_dir = .
