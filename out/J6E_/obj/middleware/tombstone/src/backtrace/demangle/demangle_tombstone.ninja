defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libdemangle_tombstone

build obj/middleware/tombstone/src/backtrace/demangle/libdemangle_tombstone.Demangler.o: cxx ../../middleware/tombstone/src/backtrace/demangle/Demangler.cpp
build obj/middleware/tombstone/src/backtrace/demangle/libdemangle_tombstone.demangle_fuzzer.o: cxx ../../middleware/tombstone/src/backtrace/demangle/demangle_fuzzer.cpp

build ./libdemangle_tombstone.a: alink obj/middleware/tombstone/src/backtrace/demangle/libdemangle_tombstone.Demangler.o obj/middleware/tombstone/src/backtrace/demangle/libdemangle_tombstone.demangle_fuzzer.o
  arflags =
  output_extension = .a
  output_dir = .
