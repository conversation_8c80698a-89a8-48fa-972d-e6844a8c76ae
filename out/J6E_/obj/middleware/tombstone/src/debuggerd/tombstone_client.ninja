defines =
include_dirs = -I../../middleware/tombstone/src/debuggerd/client/include -I../../middleware/tombstone/src/libcutils/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/tombstone/include -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -std=c++17 -pthread -Wno-unused-function -Wno-unused-variable -Wno-unused-but-set-variable -Wno-address -Wno-format -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libtombstone_client

build obj/middleware/tombstone/src/debuggerd/client/libtombstone_client.tombstone_client.o: cxx ../../middleware/tombstone/src/debuggerd/client/tombstone_client.cpp

build ./libtombstone_client.so: solink obj/middleware/tombstone/src/debuggerd/client/libtombstone_client.tombstone_client.o ./libbase_tombstone.a ./libcutils_tombstone.a
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
