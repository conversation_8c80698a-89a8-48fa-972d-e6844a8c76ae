defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fPIC -std=c++11 -pthread -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libbase_tombstone

build obj/middleware/tombstone/src/base/libbase_tombstone.chrono_utils.o: cxx ../../middleware/tombstone/src/base/chrono_utils.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.file.o: cxx ../../middleware/tombstone/src/base/file.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.logging.o: cxx ../../middleware/tombstone/src/base/logging.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.parsenetaddress.o: cxx ../../middleware/tombstone/src/base/parsenetaddress.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.quick_exit.o: cxx ../../middleware/tombstone/src/base/quick_exit.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.stringprintf.o: cxx ../../middleware/tombstone/src/base/stringprintf.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.strings_minieye.o: cxx ../../middleware/tombstone/src/base/strings_minieye.cpp
build obj/middleware/tombstone/src/base/libbase_tombstone.test_utils.o: cxx ../../middleware/tombstone/src/base/test_utils.cpp

build ./libbase_tombstone.a: alink obj/middleware/tombstone/src/base/libbase_tombstone.chrono_utils.o obj/middleware/tombstone/src/base/libbase_tombstone.file.o obj/middleware/tombstone/src/base/libbase_tombstone.logging.o obj/middleware/tombstone/src/base/libbase_tombstone.parsenetaddress.o obj/middleware/tombstone/src/base/libbase_tombstone.quick_exit.o obj/middleware/tombstone/src/base/libbase_tombstone.stringprintf.o obj/middleware/tombstone/src/base/libbase_tombstone.strings_minieye.o obj/middleware/tombstone/src/base/libbase_tombstone.test_utils.o
  arflags =
  output_extension = .a
  output_dir = .
