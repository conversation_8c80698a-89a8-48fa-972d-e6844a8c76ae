defines =
include_dirs = -I../../middleware/tombstone/src/unwindstack/include -I../../middleware/tombstone/src/backtrace/include -I../../middleware/tombstone/src/backtrace/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/unwindstack/include/unwindstack -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/tombstone/utils -I../../middleware/tombstone/utils/include -I../../middleware/tombstone/src/unwindstack -I../../middleware/tombstone/src/unwindstack/include -I../../middleware/tombstone/src/procinfo -I../../middleware/tombstone/src/procinfo/include -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src -I../../middleware/tombstone/src/base -I../../middleware/tombstone/src/base/include -I../../middleware/tombstone/src
cflags = -fPIC -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fPIC -std=c++17 -pthread -Wno-deprecated-declarations -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = backtrace_tool

build obj/middleware/tombstone/utils/backtrace_tool.backtrace_tool.o: cxx ../../middleware/tombstone/utils/backtrace_tool.cpp

build bin/backtrace_tool: link obj/middleware/tombstone/utils/backtrace_tool.backtrace_tool.o ./libbacktrace_tombstone.a ./libunwindstack_tombstone.a ./libprocinfo_tombstone.a ./libbase_tombstone.a ./liblzma_tombstone.a ./libdemangle_tombstone.a
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib
  libs =
  output_extension = 
  output_dir = bin
