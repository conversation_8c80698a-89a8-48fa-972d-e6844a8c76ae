defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/daemon/src/property -I../../middleware/daemon/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = stress_test_daemon

build obj/middleware/daemon/test/stresstest/stress_test_daemon.main.o: cxx ../../middleware/daemon/test/stresstest/main.cpp
build obj/middleware/daemon/test/stresstest/stress_test_daemon.Stresstest_ExecutionManager.o: cxx ../../middleware/daemon/test/stresstest/Stresstest_ExecutionManager.cpp
build obj/middleware/daemon/test/stresstest/stress_test_daemon.Stresstest_Property.o: cxx ../../middleware/daemon/test/stresstest/Stresstest_Property.cpp

build bin/stress_test_daemon: link obj/middleware/daemon/test/stresstest/stress_test_daemon.main.o obj/middleware/daemon/test/stresstest/stress_test_daemon.Stresstest_ExecutionManager.o obj/middleware/daemon/test/stresstest/stress_test_daemon.Stresstest_Property.o ./libproperty.so ./libdaemon.so ./libmlog.so
  ldflags = -lgtest -lgtest_main -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
