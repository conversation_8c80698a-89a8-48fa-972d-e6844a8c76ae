defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/daemon/src/property -I../../middleware/daemon/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = unittest_daemon

build obj/middleware/daemon/test/unittest/unittest_daemon.main.o: cxx ../../middleware/daemon/test/unittest/main.cpp
build obj/middleware/daemon/test/unittest/unittest_daemon.Test_Property.o: cxx ../../middleware/daemon/test/unittest/Test_Property.cpp
build obj/middleware/daemon/test/unittest/unittest_daemon.Test_ExecutionManager.o: cxx ../../middleware/daemon/test/unittest/Test_ExecutionManager.cpp

build bin/unittest_daemon: link obj/middleware/daemon/test/unittest/unittest_daemon.main.o obj/middleware/daemon/test/unittest/unittest_daemon.Test_Property.o obj/middleware/daemon/test/unittest/unittest_daemon.Test_ExecutionManager.o ./libproperty.so ./libdaemon.so ./libmlog.so
  ldflags = -lgtest -lgtest_main -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
