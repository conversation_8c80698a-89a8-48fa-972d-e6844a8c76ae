defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -fpermissive -std=c++11 -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libproperty

build obj/middleware/daemon/src/property/libproperty.properties.o: cxx ../../middleware/daemon/src/property/properties.cpp
build obj/middleware/daemon/src/property/libproperty.system_properties.o: cxx ../../middleware/daemon/src/property/system_properties.cpp
build obj/middleware/daemon/src/property/libproperty.nn_sub.o: cxx ../../middleware/daemon/src/property/nn_sub.cpp

build ./libproperty.so: solink obj/middleware/daemon/src/property/libproperty.properties.o obj/middleware/daemon/src/property/libproperty.system_properties.o obj/middleware/daemon/src/property/libproperty.nn_sub.o
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
