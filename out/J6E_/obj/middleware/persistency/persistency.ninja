defines =
include_dirs = -I../../middleware/persistency/src -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = persistency

build obj/middleware/persistency/src/server/persistency.main.o: cxx ../../middleware/persistency/src/server/main.cpp
build obj/middleware/persistency/src/server/persistency.ParamsHandler.o: cxx ../../middleware/persistency/src/server/ParamsHandler.cpp
build obj/middleware/persistency/src/server/persistency.Persistency.o: cxx ../../middleware/persistency/src/server/Persistency.cpp

build bin/persistency: link obj/middleware/persistency/src/server/persistency.main.o obj/middleware/persistency/src/server/persistency.ParamsHandler.o obj/middleware/persistency/src/server/persistency.Persistency.o ./libpersistency_common.so ./libmlog.so
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
