defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = unittest_persistency

build obj/middleware/persistency/test/unittest/unittest_persistency.main.o: cxx ../../middleware/persistency/test/unittest/main.cpp
build obj/middleware/persistency/test/unittest/unittest_persistency.Test_AtomicFile.o: cxx ../../middleware/persistency/test/unittest/Test_AtomicFile.cpp
build obj/middleware/persistency/test/unittest/unittest_persistency.Test_Parameters.o: cxx ../../middleware/persistency/test/unittest/Test_Parameters.cpp
build obj/middleware/persistency/test/unittest/unittest_persistency.Test_ShareConfigImpl.o: cxx ../../middleware/persistency/test/unittest/Test_ShareConfigImpl.cpp
build obj/middleware/persistency/test/unittest/unittest_persistency.Test_Persistency.o: cxx ../../middleware/persistency/test/unittest/Test_Persistency.cpp
build obj/middleware/persistency/test/unittest/unittest_persistency.Test_SystemProp.o: cxx ../../middleware/persistency/test/unittest/Test_SystemProp.cpp

build bin/unittest_persistency: link obj/middleware/persistency/test/unittest/unittest_persistency.main.o obj/middleware/persistency/test/unittest/unittest_persistency.Test_AtomicFile.o obj/middleware/persistency/test/unittest/unittest_persistency.Test_Parameters.o obj/middleware/persistency/test/unittest/unittest_persistency.Test_ShareConfigImpl.o obj/middleware/persistency/test/unittest/unittest_persistency.Test_Persistency.o obj/middleware/persistency/test/unittest/unittest_persistency.Test_SystemProp.o ./libpersistency.so ./libpersistency_common.so ./libmlog.so
  ldflags = -lgtest -lgtest_main -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
