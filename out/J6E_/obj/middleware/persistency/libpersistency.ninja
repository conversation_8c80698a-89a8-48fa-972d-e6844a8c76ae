defines =
include_dirs = -I../../middleware/persistency/src -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libpersistency

build obj/middleware/persistency/src/libclient/libpersistency.ShareConfigImpl.o: cxx ../../middleware/persistency/src/libclient/ShareConfigImpl.cpp
build obj/middleware/persistency/src/libclient/libpersistency.AtomicFile.o: cxx ../../middleware/persistency/src/libclient/AtomicFile.cpp
build obj/middleware/persistency/src/libclient/libpersistency.SystemProp.o: cxx ../../middleware/persistency/src/libclient/SystemProp.cpp

build ./libpersistency.so: solink obj/middleware/persistency/src/libclient/libpersistency.ShareConfigImpl.o obj/middleware/persistency/src/libclient/libpersistency.AtomicFile.o obj/middleware/persistency/src/libclient/libpersistency.SystemProp.o ./libpersistency_common.so ./libmlog.so
  ldflags = -ldl -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
