defines =
include_dirs = -I../../middleware/persistency/src -I../../platform/J6E/build/sysroots/usr/include/msgpack-c -I../../platform/J6E/build/sysroots/usr/include/flow -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/persistency/include -I../../middleware/mlog/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libpersistency_common

build obj/middleware/persistency/src/common/io/libpersistency_common.AtomicIOUtilFactory.o: cxx ../../middleware/persistency/src/common/io/AtomicIOUtilFactory.cpp
build obj/middleware/persistency/src/common/io/libpersistency_common.FileIOUtil.o: cxx ../../middleware/persistency/src/common/io/FileIOUtil.cpp
build obj/middleware/persistency/src/common/io/libpersistency_common.AtomicIOUtil.o: cxx ../../middleware/persistency/src/common/io/AtomicIOUtil.cpp
build obj/middleware/persistency/src/common/io/libpersistency_common.BlkIOUtil.o: cxx ../../middleware/persistency/src/common/io/BlkIOUtil.cpp
build obj/middleware/persistency/src/common/libpersistency_common.Parameters.o: cxx ../../middleware/persistency/src/common/Parameters.cpp
build obj/middleware/persistency/src/common/libpersistency_common.Utils.o: cxx ../../middleware/persistency/src/common/Utils.cpp
build obj/middleware/persistency/src/common/libpersistency_common.ParamsLoader.o: cxx ../../middleware/persistency/src/common/ParamsLoader.cpp
build obj/middleware/persistency/src/common/libpersistency_common.ParamsWriter.o: cxx ../../middleware/persistency/src/common/ParamsWriter.cpp
build obj/middleware/persistency/src/common/libpersistency_common.GflagHandler.o: cxx ../../middleware/persistency/src/common/GflagHandler.cpp
build obj/middleware/persistency/src/common/libpersistency_common.ErrorCode.o: cxx ../../middleware/persistency/src/common/ErrorCode.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.Publisher.o: cxx ../../middleware/persistency/src/connection/Publisher.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.Packet.o: cxx ../../middleware/persistency/src/connection/Packet.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.ParamsPublisher.o: cxx ../../middleware/persistency/src/connection/ParamsPublisher.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.Subscriber.o: cxx ../../middleware/persistency/src/connection/Subscriber.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.ParamsSubscriber.o: cxx ../../middleware/persistency/src/connection/ParamsSubscriber.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.Client.o: cxx ../../middleware/persistency/src/connection/Client.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.ClientImpl.o: cxx ../../middleware/persistency/src/connection/ClientImpl.cpp
build obj/middleware/persistency/src/connection/libpersistency_common.Server.o: cxx ../../middleware/persistency/src/connection/Server.cpp

build ./libpersistency_common.so: solink obj/middleware/persistency/src/common/io/libpersistency_common.AtomicIOUtilFactory.o obj/middleware/persistency/src/common/io/libpersistency_common.FileIOUtil.o obj/middleware/persistency/src/common/io/libpersistency_common.AtomicIOUtil.o obj/middleware/persistency/src/common/io/libpersistency_common.BlkIOUtil.o obj/middleware/persistency/src/common/libpersistency_common.Parameters.o obj/middleware/persistency/src/common/libpersistency_common.Utils.o obj/middleware/persistency/src/common/libpersistency_common.ParamsLoader.o obj/middleware/persistency/src/common/libpersistency_common.ParamsWriter.o obj/middleware/persistency/src/common/libpersistency_common.GflagHandler.o obj/middleware/persistency/src/common/libpersistency_common.ErrorCode.o obj/middleware/persistency/src/connection/libpersistency_common.Publisher.o obj/middleware/persistency/src/connection/libpersistency_common.Packet.o obj/middleware/persistency/src/connection/libpersistency_common.ParamsPublisher.o obj/middleware/persistency/src/connection/libpersistency_common.Subscriber.o obj/middleware/persistency/src/connection/libpersistency_common.ParamsSubscriber.o obj/middleware/persistency/src/connection/libpersistency_common.Client.o obj/middleware/persistency/src/connection/libpersistency_common.ClientImpl.o obj/middleware/persistency/src/connection/libpersistency_common.Server.o ./libmlog.so
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
