defines =
include_dirs = -I../../middleware/communication/nanomsg/src -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/communication/nanomsg/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = libnnmsg

build obj/middleware/communication/nanomsg/src/libnnmsg.NnPairImpl.o: cxx ../../middleware/communication/nanomsg/src/NnPairImpl.cpp
build obj/middleware/communication/nanomsg/src/libnnmsg.NnPubSubImpl.o: cxx ../../middleware/communication/nanomsg/src/NnPubSubImpl.cpp
build obj/middleware/communication/nanomsg/src/libnnmsg.NnReqRepImpl.o: cxx ../../middleware/communication/nanomsg/src/NnReqRepImpl.cpp
build obj/middleware/communication/nanomsg/src/libnnmsg.NnSurveyResImpl.o: cxx ../../middleware/communication/nanomsg/src/NnSurveyResImpl.cpp

build ./libnnmsg.so: solink obj/middleware/communication/nanomsg/src/libnnmsg.NnPairImpl.o obj/middleware/communication/nanomsg/src/libnnmsg.NnPubSubImpl.o obj/middleware/communication/nanomsg/src/libnnmsg.NnReqRepImpl.o obj/middleware/communication/nanomsg/src/libnnmsg.NnSurveyResImpl.o
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
