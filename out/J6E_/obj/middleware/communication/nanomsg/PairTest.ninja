defines =
include_dirs = -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/communication/nanomsg/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = PairTest

build obj/middleware/communication/nanomsg/sample/PairTest.PairTest.o: cxx ../../middleware/communication/nanomsg/sample/PairTest.cpp

build bin/PairTest: link obj/middleware/communication/nanomsg/sample/PairTest.PairTest.o ./libnnmsg.so
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
