defines =
include_dirs = -I../../middleware/logd/src/pcre -I../../middleware/logd/src/pcre/dist -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -std=c++11 -Wno-misleading-indentation -Wno-unused-variable -Wno-maybe-uninitialized -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libpcrecpp_logd

build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcrecpp.o: cxx ../../middleware/logd/src/pcre/dist/pcrecpp.cc
build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_scanner.o: cxx ../../middleware/logd/src/pcre/dist/pcre_scanner.cc
build obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_stringpiece.o: cxx ../../middleware/logd/src/pcre/dist/pcre_stringpiece.cc

build ./libpcrecpp_logd.a: alink obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcrecpp.o obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_scanner.o obj/middleware/logd/src/pcre/dist/libpcrecpp_logd.pcre_stringpiece.o || ./libpcre_logd.a
  arflags =
  output_extension = .a
  output_dir = .
