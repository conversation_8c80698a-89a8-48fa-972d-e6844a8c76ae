defines =
include_dirs = -I../../middleware/logd/src/base/include -I../../middleware/logd/src/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-comment -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libbase_logd

build obj/middleware/logd/src/base/libbase_logd.file.o: cxx ../../middleware/logd/src/base/file.cpp
build obj/middleware/logd/src/base/libbase_logd.logging.o: cxx ../../middleware/logd/src/base/logging.cpp
build obj/middleware/logd/src/base/libbase_logd.parsenetaddress.o: cxx ../../middleware/logd/src/base/parsenetaddress.cpp
build obj/middleware/logd/src/base/libbase_logd.stringprintf.o: cxx ../../middleware/logd/src/base/stringprintf.cpp
build obj/middleware/logd/src/base/libbase_logd.strings.o: cxx ../../middleware/logd/src/base/strings.cpp
build obj/middleware/logd/src/base/libbase_logd.errors_unix.o: cxx ../../middleware/logd/src/base/errors_unix.cpp

build ./libbase_logd.a: alink obj/middleware/logd/src/base/libbase_logd.file.o obj/middleware/logd/src/base/libbase_logd.logging.o obj/middleware/logd/src/base/libbase_logd.parsenetaddress.o obj/middleware/logd/src/base/libbase_logd.stringprintf.o obj/middleware/logd/src/base/libbase_logd.strings.o obj/middleware/logd/src/base/libbase_logd.errors_unix.o
  arflags =
  output_extension = .a
  output_dir = .
