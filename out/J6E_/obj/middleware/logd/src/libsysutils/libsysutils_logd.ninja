defines =
include_dirs = -I../../middleware/logd/src/libsysutils/include -I../../middleware/logd/src/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fpermissive -std=c++11 -Wno-unused-result -Wno-comment -Wno-implicit-fallthrough -Wno-unused-variable -Wno-deprecated-copy -Wno-format-overflow -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libsysutils_logd

build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.SocketListener.o: cxx ../../middleware/logd/src/libsysutils/src/SocketListener.cpp
build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.FrameworkListener.o: cxx ../../middleware/logd/src/libsysutils/src/FrameworkListener.cpp
build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.NetlinkListener.o: cxx ../../middleware/logd/src/libsysutils/src/NetlinkListener.cpp
build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.NetlinkEvent.o: cxx ../../middleware/logd/src/libsysutils/src/NetlinkEvent.cpp
build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.FrameworkCommand.o: cxx ../../middleware/logd/src/libsysutils/src/FrameworkCommand.cpp
build obj/middleware/logd/src/libsysutils/src/libsysutils_logd.SocketClient.o: cxx ../../middleware/logd/src/libsysutils/src/SocketClient.cpp

build ./libsysutils_logd.a: alink obj/middleware/logd/src/libsysutils/src/libsysutils_logd.SocketListener.o obj/middleware/logd/src/libsysutils/src/libsysutils_logd.FrameworkListener.o obj/middleware/logd/src/libsysutils/src/libsysutils_logd.NetlinkListener.o obj/middleware/logd/src/libsysutils/src/libsysutils_logd.NetlinkEvent.o obj/middleware/logd/src/libsysutils/src/libsysutils_logd.FrameworkCommand.o obj/middleware/logd/src/libsysutils/src/libsysutils_logd.SocketClient.o
  arflags =
  output_extension = .a
  output_dir = .
