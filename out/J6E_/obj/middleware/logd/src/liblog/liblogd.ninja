defines =
include_dirs = -I../../middleware/logd/src/liblog/include -I../../middleware/logd/src/include -I../../middleware/logd/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
cflags_c = -std=c11 -Wno-comment -Wno-int-conversion -Wno-unused-variable -Wno-strict-aliasing -Wno-sign-compare -Wno-unused-result -Wno-implicit-function-declaration -Wno-cpp -Wno-unused-but-set-variable -Wno-uninitialized
cflags_cc = -fpermissive -std=c++11 -Wno-comment -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIC
target_output_name = liblogd

build obj/middleware/logd/src/liblog/liblogd.log_event_list.o: cc ../../middleware/logd/src/liblog/log_event_list.c
build obj/middleware/logd/src/liblog/liblogd.log_event_write.o: cc ../../middleware/logd/src/liblog/log_event_write.c
build obj/middleware/logd/src/liblog/liblogd.logger_write.o: cc ../../middleware/logd/src/liblog/logger_write.c
build obj/middleware/logd/src/liblog/liblogd.config_write.o: cc ../../middleware/logd/src/liblog/config_write.c
build obj/middleware/logd/src/liblog/liblogd.logger_name.o: cc ../../middleware/logd/src/liblog/logger_name.c
build obj/middleware/logd/src/liblog/liblogd.logger_lock.o: cc ../../middleware/logd/src/liblog/logger_lock.c
build obj/middleware/logd/src/liblog/liblogd.event_tag_map.o: cc ../../middleware/logd/src/liblog/event_tag_map.c
build obj/middleware/logd/src/liblog/liblogd.config_read.o: cc ../../middleware/logd/src/liblog/config_read.c
build obj/middleware/logd/src/liblog/liblogd.log_time.o: cxx ../../middleware/logd/src/liblog/log_time.cpp
build obj/middleware/logd/src/liblog/liblogd.log_is_loggable.o: cc ../../middleware/logd/src/liblog/log_is_loggable.c
build obj/middleware/logd/src/liblog/liblogd.logprint.o: cc ../../middleware/logd/src/liblog/logprint.c
build obj/middleware/logd/src/liblog/liblogd.pmsg_reader.o: cc ../../middleware/logd/src/liblog/pmsg_reader.c
build obj/middleware/logd/src/liblog/liblogd.pmsg_writer.o: cc ../../middleware/logd/src/liblog/pmsg_writer.c
build obj/middleware/logd/src/liblog/liblogd.logd_reader.o: cc ../../middleware/logd/src/liblog/logd_reader.c
build obj/middleware/logd/src/liblog/liblogd.logd_writer.o: cc ../../middleware/logd/src/liblog/logd_writer.c
build obj/middleware/logd/src/liblog/liblogd.logger_read.o: cc ../../middleware/logd/src/liblog/logger_read.c
build obj/middleware/logd/src/liblog/liblogd.minieye_liblogd.o: cxx ../../middleware/logd/src/liblog/minieye_liblogd.cpp

build ./liblogd.so: solink obj/middleware/logd/src/liblog/liblogd.log_event_list.o obj/middleware/logd/src/liblog/liblogd.log_event_write.o obj/middleware/logd/src/liblog/liblogd.logger_write.o obj/middleware/logd/src/liblog/liblogd.config_write.o obj/middleware/logd/src/liblog/liblogd.logger_name.o obj/middleware/logd/src/liblog/liblogd.logger_lock.o obj/middleware/logd/src/liblog/liblogd.event_tag_map.o obj/middleware/logd/src/liblog/liblogd.config_read.o obj/middleware/logd/src/liblog/liblogd.log_time.o obj/middleware/logd/src/liblog/liblogd.log_is_loggable.o obj/middleware/logd/src/liblog/liblogd.logprint.o obj/middleware/logd/src/liblog/liblogd.pmsg_reader.o obj/middleware/logd/src/liblog/liblogd.pmsg_writer.o obj/middleware/logd/src/liblog/liblogd.logd_reader.o obj/middleware/logd/src/liblog/liblogd.logd_writer.o obj/middleware/logd/src/liblog/liblogd.logger_read.o obj/middleware/logd/src/liblog/liblogd.minieye_liblogd.o
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib
  libs =
  output_extension = .so
  output_dir = .
