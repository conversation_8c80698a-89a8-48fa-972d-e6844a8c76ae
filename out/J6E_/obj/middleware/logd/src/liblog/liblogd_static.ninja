defines =
include_dirs = -I../../middleware/logd/src/liblog/include -I../../middleware/logd/src/include -I../../middleware/logd/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_c = -std=c11 -Wno-comment -Wno-int-conversion -Wno-unused-variable -Wno-strict-aliasing -Wno-sign-compare -Wno-unused-result -Wno-implicit-function-declaration -Wno-cpp -Wno-unused-but-set-variable -Wno-uninitialized
cflags_cc = -fpermissive -std=c++11 -Wno-comment -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = liblogd_static

build obj/middleware/logd/src/liblog/liblogd_static.log_event_list.o: cc ../../middleware/logd/src/liblog/log_event_list.c
build obj/middleware/logd/src/liblog/liblogd_static.log_event_write.o: cc ../../middleware/logd/src/liblog/log_event_write.c
build obj/middleware/logd/src/liblog/liblogd_static.logger_write.o: cc ../../middleware/logd/src/liblog/logger_write.c
build obj/middleware/logd/src/liblog/liblogd_static.config_write.o: cc ../../middleware/logd/src/liblog/config_write.c
build obj/middleware/logd/src/liblog/liblogd_static.logger_name.o: cc ../../middleware/logd/src/liblog/logger_name.c
build obj/middleware/logd/src/liblog/liblogd_static.logger_lock.o: cc ../../middleware/logd/src/liblog/logger_lock.c
build obj/middleware/logd/src/liblog/liblogd_static.event_tag_map.o: cc ../../middleware/logd/src/liblog/event_tag_map.c
build obj/middleware/logd/src/liblog/liblogd_static.config_read.o: cc ../../middleware/logd/src/liblog/config_read.c
build obj/middleware/logd/src/liblog/liblogd_static.log_time.o: cxx ../../middleware/logd/src/liblog/log_time.cpp
build obj/middleware/logd/src/liblog/liblogd_static.log_is_loggable.o: cc ../../middleware/logd/src/liblog/log_is_loggable.c
build obj/middleware/logd/src/liblog/liblogd_static.logprint.o: cc ../../middleware/logd/src/liblog/logprint.c
build obj/middleware/logd/src/liblog/liblogd_static.pmsg_reader.o: cc ../../middleware/logd/src/liblog/pmsg_reader.c
build obj/middleware/logd/src/liblog/liblogd_static.pmsg_writer.o: cc ../../middleware/logd/src/liblog/pmsg_writer.c
build obj/middleware/logd/src/liblog/liblogd_static.logd_reader.o: cc ../../middleware/logd/src/liblog/logd_reader.c
build obj/middleware/logd/src/liblog/liblogd_static.logd_writer.o: cc ../../middleware/logd/src/liblog/logd_writer.c
build obj/middleware/logd/src/liblog/liblogd_static.logger_read.o: cc ../../middleware/logd/src/liblog/logger_read.c
build obj/middleware/logd/src/liblog/liblogd_static.minieye_liblogd.o: cxx ../../middleware/logd/src/liblog/minieye_liblogd.cpp

build ./liblogd_static.a: alink obj/middleware/logd/src/liblog/liblogd_static.log_event_list.o obj/middleware/logd/src/liblog/liblogd_static.log_event_write.o obj/middleware/logd/src/liblog/liblogd_static.logger_write.o obj/middleware/logd/src/liblog/liblogd_static.config_write.o obj/middleware/logd/src/liblog/liblogd_static.logger_name.o obj/middleware/logd/src/liblog/liblogd_static.logger_lock.o obj/middleware/logd/src/liblog/liblogd_static.event_tag_map.o obj/middleware/logd/src/liblog/liblogd_static.config_read.o obj/middleware/logd/src/liblog/liblogd_static.log_time.o obj/middleware/logd/src/liblog/liblogd_static.log_is_loggable.o obj/middleware/logd/src/liblog/liblogd_static.logprint.o obj/middleware/logd/src/liblog/liblogd_static.pmsg_reader.o obj/middleware/logd/src/liblog/liblogd_static.pmsg_writer.o obj/middleware/logd/src/liblog/liblogd_static.logd_reader.o obj/middleware/logd/src/liblog/liblogd_static.logd_writer.o obj/middleware/logd/src/liblog/liblogd_static.logger_read.o obj/middleware/logd/src/liblog/liblogd_static.minieye_liblogd.o
  arflags =
  output_extension = .a
  output_dir = .
