defines =
include_dirs = -I../../middleware/logd/src/include -I../../middleware/logd/src/base/include -I../../middleware/logd/src/pcre/dist -I../../middleware/logd/src/pcre -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include -I../../middleware/logd/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_cc = -fpermissive -std=c++17 -Wno-implicit-fallthrough -Wno-unused-function -Wno-unused-variable -Wno-comment -Wno-unused-result -Wno-format -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = logdcat

build obj/middleware/logd/src/logcat/logdcat.logcat.o: cxx ../../middleware/logd/src/logcat/logcat.cpp

build bin/logdcat: link obj/middleware/logd/src/logcat/logdcat.logcat.o ./liblogd.so ./libcutils_logd.a ./libbase_logd.a ./libpcrecpp_logd.a ./libpcre_logd.a
  ldflags = -L/home/<USER>/mine/out/J6E_ -Wl,-rpath-link=/home/<USER>/mine/out/J6E_ -lpthread --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/minieye/lib -L/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib -Wl,-rpath-link=/home/<USER>/mine/platform/J6E/build/sysroots/usr/hobot/lib
  libs =
  output_extension = 
  output_dir = bin
