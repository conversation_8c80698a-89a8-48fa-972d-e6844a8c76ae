defines =
include_dirs = -I../../middleware/logd/src/include -I../../platform/J6E/build/sysroots/usr/minieye/include -I../../platform/J6E/build/sysroots/usr/hobot/include
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
cflags_c = -pthread -Wno-comment
cflags_cc = -std=c++17 -Wno-comment -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/J6E/build/sysroots -fPIE
target_output_name = libcutils_logd

build obj/middleware/logd/src/libcutils/libcutils_logd.config_utils.o: cc ../../middleware/logd/src/libcutils/config_utils.c
build obj/middleware/logd/src/libcutils/libcutils_logd.hashmap.o: cc ../../middleware/logd/src/libcutils/hashmap.c
build obj/middleware/logd/src/libcutils/libcutils_logd.iosched_policy.o: cc ../../middleware/logd/src/libcutils/iosched_policy.c
build obj/middleware/logd/src/libcutils/libcutils_logd.load_file.o: cc ../../middleware/logd/src/libcutils/load_file.c
build obj/middleware/logd/src/libcutils/libcutils_logd.native_handle.o: cc ../../middleware/logd/src/libcutils/native_handle.c
build obj/middleware/logd/src/libcutils/libcutils_logd.open_memstream.o: cc ../../middleware/logd/src/libcutils/open_memstream.c
build obj/middleware/logd/src/libcutils/libcutils_logd.process_name.o: cc ../../middleware/logd/src/libcutils/process_name.c
build obj/middleware/logd/src/libcutils/libcutils_logd.record_stream.o: cc ../../middleware/logd/src/libcutils/record_stream.c
build obj/middleware/logd/src/libcutils/libcutils_logd.sockets.o: cxx ../../middleware/logd/src/libcutils/sockets.cpp
build obj/middleware/logd/src/libcutils/libcutils_logd.strlcpy.o: cc ../../middleware/logd/src/libcutils/strlcpy.c
build obj/middleware/logd/src/libcutils/libcutils_logd.threads.o: cc ../../middleware/logd/src/libcutils/threads.c
build obj/middleware/logd/src/libcutils/libcutils_logd.multiuser.o: cc ../../middleware/logd/src/libcutils/multiuser.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_inaddr_any_server_unix.o: cc ../../middleware/logd/src/libcutils/socket_inaddr_any_server_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_local_client_unix.o: cc ../../middleware/logd/src/libcutils/socket_local_client_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_local_server_unix.o: cc ../../middleware/logd/src/libcutils/socket_local_server_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_loopback_client_unix.o: cc ../../middleware/logd/src/libcutils/socket_loopback_client_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_loopback_server_unix.o: cc ../../middleware/logd/src/libcutils/socket_loopback_server_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.socket_network_client_unix.o: cc ../../middleware/logd/src/libcutils/socket_network_client_unix.c
build obj/middleware/logd/src/libcutils/libcutils_logd.sockets_unix.o: cxx ../../middleware/logd/src/libcutils/sockets_unix.cpp
build obj/middleware/logd/src/libcutils/libcutils_logd.sched_policy.o: cc ../../middleware/logd/src/libcutils/sched_policy.c

build ./libcutils_logd.a: alink obj/middleware/logd/src/libcutils/libcutils_logd.config_utils.o obj/middleware/logd/src/libcutils/libcutils_logd.hashmap.o obj/middleware/logd/src/libcutils/libcutils_logd.iosched_policy.o obj/middleware/logd/src/libcutils/libcutils_logd.load_file.o obj/middleware/logd/src/libcutils/libcutils_logd.native_handle.o obj/middleware/logd/src/libcutils/libcutils_logd.open_memstream.o obj/middleware/logd/src/libcutils/libcutils_logd.process_name.o obj/middleware/logd/src/libcutils/libcutils_logd.record_stream.o obj/middleware/logd/src/libcutils/libcutils_logd.sockets.o obj/middleware/logd/src/libcutils/libcutils_logd.strlcpy.o obj/middleware/logd/src/libcutils/libcutils_logd.threads.o obj/middleware/logd/src/libcutils/libcutils_logd.multiuser.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_inaddr_any_server_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_local_client_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_local_server_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_loopback_client_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_loopback_server_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.socket_network_client_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.sockets_unix.o obj/middleware/logd/src/libcutils/libcutils_logd.sched_policy.o
  arflags =
  output_extension = .a
  output_dir = .
