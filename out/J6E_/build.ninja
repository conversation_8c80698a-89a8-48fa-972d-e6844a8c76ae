ninja_required_version = 1.7.2

rule gn
  command = ../../build/minieye/tools/gn --root=../.. -q --dotfile=../../build/minieye/.gn gen .
  description = Regenerating ninja files

build build.ninja: gn
  generator = 1
  depfile = build.ninja.d

subninja toolchain.ninja

build PairTest: phony bin/PairTest
build PubSubTest: phony bin/PubSubTest
build ReqRepTest: phony bin/ReqRepTest
build SurveyTest: phony bin/SurveyTest
build backtrace_tool: phony bin/backtrace_tool
build classtest_persistency: phony bin/classtest_persistency
build daemon: phony bin/daemon
build dumprc: phony bin/dumprc
build dumpsys: phony bin/dumpsys
build file_monitor: phony bin/file_monitor
build getshareconfig: phony bin/getshareconfig
build getsystemprop: phony bin/getsystemprop
build logd: phony bin/logd
build logdcat: phony bin/logdcat
build mlogcat: phony bin/mlogcat
build mlogdeobf: phony bin/mlogdeobf
build mlogsend: phony bin/mlogsend
build performance: phony bin/performance
build persistency: phony bin/persistency
build sample_atomicfile: phony bin/sample_atomicfile
build sample_dumpsys: phony bin/sample_dumpsys
build sample_em: phony bin/sample_em
build sample_logd: phony bin/sample_logd
build sample_message: phony bin/sample_message
build sample_mlog: phony bin/sample_mlog
build sample_shareconfig: phony bin/sample_shareconfig
build sample_systemprop: phony bin/sample_systemprop
build sample_threadpool: phony bin/sample_threadpool
build sample_tombstone: phony bin/sample_tombstone
build scantree: phony bin/scantree
build setshareconfig: phony bin/setshareconfig
build setsystemprop: phony bin/setsystemprop
build shareconfigcrc: phony bin/shareconfigcrc
build shareconfigstress: phony bin/shareconfigstress
build startrc: phony bin/startrc
build stoprc: phony bin/stoprc
build stress_test_daemon: phony bin/stress_test_daemon
build test: phony bin/test
build tombstone: phony bin/tombstone
build unittest_daemon: phony bin/unittest_daemon
build unittest_daemon_sh: phony bin/unittest_daemon_sh
build unittest_mlog: phony bin/unittest_mlog
build unittest_persistency: phony bin/unittest_persistency
build backtrace_tombstone: phony ./libbacktrace_tombstone.a
build base_logd: phony ./libbase_logd.a
build base_tombstone: phony ./libbase_tombstone.a
build cppbase: phony ./libcppbase.so
build daemon_group: phony obj/middleware/daemon/daemon_group.stamp
build daemon_sample: phony obj/middleware/daemon/sample/daemon_sample.stamp
build daemon_test: phony obj/middleware/daemon/test/daemon_test.stamp
build demangle_tombstone: phony ./libdemangle_tombstone.a
build dumpsys_group: phony obj/middleware/dumpsys/dumpsys_group.stamp
build libcutils_logd: phony ./libcutils_logd.a
build libcutils_tombstone: phony ./libcutils_tombstone.a
build libdumpsys_interface: phony ./libdumpsys_interface.so
build liblogd: phony ./liblogd.so
build liblogd_static: phony ./liblogd_static.a
build libmlog: phony ./libmlog.so
build libmlog_static: phony ./libmlog_static.a
build libnnmsg: phony ./libnnmsg.so
build libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build libpcre_logd: phony ./libpcre_logd.a
build libpcrecpp_logd: phony ./libpcrecpp_logd.a
build libpersistency: phony ./libpersistency.so
build libpersistency_common: phony ./libpersistency_common.so
build library_nanomsg_group: phony obj/middleware/communication/nanomsg/library_nanomsg_group.stamp
build libsysutils_logd: phony ./libsysutils_logd.a
build logd_group: phony obj/middleware/logd/logd_group.stamp
build lzma_tombstone: phony ./liblzma_tombstone.a
build message: phony ./libmessage.so
build mlog_group: phony obj/middleware/mlog/mlog_group.stamp
build mlog_sample: phony obj/middleware/mlog/sample/mlog_sample.stamp
build mlog_test: phony obj/middleware/mlog/test/mlog_test.stamp
build persistency_group: phony obj/middleware/persistency/persistency_group.stamp
build persistency_sample: phony obj/middleware/persistency/sample/persistency_sample.stamp
build persistency_test: phony obj/middleware/persistency/test/persistency_test.stamp
build procinfo_tombstone: phony ./libprocinfo_tombstone.a
build property: phony ./libproperty.so
build tombstone_client: phony ./libtombstone_client.so
build tombstone_group: phony obj/middleware/tombstone/tombstone_group.stamp
build unwindstack_tombstone: phony ./libunwindstack_tombstone.a
build build/minieye$:all: phony obj/build/minieye/all.stamp
build middleware/communication/nanomsg$:PairTest: phony bin/PairTest
build middleware/communication/nanomsg$:PubSubTest: phony bin/PubSubTest
build middleware/communication/nanomsg$:ReqRepTest: phony bin/ReqRepTest
build middleware/communication/nanomsg$:SurveyTest: phony bin/SurveyTest
build middleware/communication/nanomsg$:libnnmsg: phony ./libnnmsg.so
build middleware/communication/nanomsg$:library_nanomsg_group: phony obj/middleware/communication/nanomsg/library_nanomsg_group.stamp
build middleware/daemon$:daemon_group: phony obj/middleware/daemon/daemon_group.stamp
build middleware/daemon/sample$:daemon_sample: phony obj/middleware/daemon/sample/daemon_sample.stamp
build middleware/daemon/sample$:sample_em: phony bin/sample_em
build middleware/daemon/src/em$:daemon: phony ./libdaemon.so
build middleware/daemon/src/property$:property: phony ./libproperty.so
build middleware/daemon/src/property: phony ./libproperty.so
build middleware/daemon/src/server$:daemon: phony bin/daemon
build middleware/daemon/test$:daemon_test: phony obj/middleware/daemon/test/daemon_test.stamp
build middleware/daemon/test$:stress_test_daemon: phony bin/stress_test_daemon
build middleware/daemon/test$:unittest_daemon: phony bin/unittest_daemon
build middleware/daemon/test$:unittest_daemon_sh: phony bin/unittest_daemon_sh
build middleware/daemon/utils$:dumprc: phony bin/dumprc
build middleware/daemon/utils$:getsystemprop: phony bin/getsystemprop
build middleware/daemon/utils$:setsystemprop: phony bin/setsystemprop
build middleware/daemon/utils$:startrc: phony bin/startrc
build middleware/daemon/utils$:stoprc: phony bin/stoprc
build middleware/daemon/utils$:utils: phony obj/middleware/daemon/utils/utils.stamp
build middleware/daemon/utils: phony obj/middleware/daemon/utils/utils.stamp
build middleware/dumpsys$:dumpsys: phony bin/dumpsys
build middleware/dumpsys: phony bin/dumpsys
build middleware/dumpsys$:dumpsys_group: phony obj/middleware/dumpsys/dumpsys_group.stamp
build middleware/dumpsys$:libdumpsys_interface: phony ./libdumpsys_interface.so
build middleware/dumpsys$:sample_dumpsys: phony bin/sample_dumpsys
build middleware/logd$:logd_group: phony obj/middleware/logd/logd_group.stamp
build middleware/logd/sample$:sample_logd: phony bin/sample_logd
build middleware/logd/src/base$:base_logd: phony ./libbase_logd.a
build middleware/logd/src/libcutils$:libcutils_logd: phony ./libcutils_logd.a
build middleware/logd/src/liblog$:liblogd: phony ./liblogd.so
build middleware/logd/src/liblog$:liblogd_static: phony ./liblogd_static.a
build middleware/logd/src/libpackagelistparser$:libpackagelistparser_logd: phony ./libpackagelistparser_logd.a
build middleware/logd/src/libsysutils$:libsysutils_logd: phony ./libsysutils_logd.a
build middleware/logd/src/logcat$:logdcat: phony bin/logdcat
build middleware/logd/src/logd$:logd: phony bin/logd
build middleware/logd/src/logd: phony bin/logd
build middleware/logd/src/pcre$:libpcre_logd: phony ./libpcre_logd.a
build middleware/logd/src/pcre$:libpcrecpp_logd: phony ./libpcrecpp_logd.a
build middleware/mlog$:libmlog: phony ./libmlog.so
build middleware/mlog$:libmlog_static: phony ./libmlog_static.a
build middleware/mlog$:mlog_group: phony obj/middleware/mlog/mlog_group.stamp
build middleware/mlog$:mlogcat: phony bin/mlogcat
build middleware/mlog$:mlogdeobf: phony bin/mlogdeobf
build middleware/mlog/sample$:mlog_sample: phony obj/middleware/mlog/sample/mlog_sample.stamp
build middleware/mlog/sample$:mlogsend: phony bin/mlogsend
build middleware/mlog/sample$:sample_mlog: phony bin/sample_mlog
build middleware/mlog/test$:mlog_test: phony obj/middleware/mlog/test/mlog_test.stamp
build middleware/mlog/test$:performance: phony bin/performance
build middleware/mlog/test$:unittest_mlog: phony bin/unittest_mlog
build middleware/persistency$:libpersistency: phony ./libpersistency.so
build middleware/persistency$:libpersistency_common: phony ./libpersistency_common.so
build middleware/persistency$:persistency: phony bin/persistency
build middleware/persistency: phony bin/persistency
build middleware/persistency$:persistency_group: phony obj/middleware/persistency/persistency_group.stamp
build middleware/persistency/sample$:persistency_sample: phony obj/middleware/persistency/sample/persistency_sample.stamp
build middleware/persistency/sample$:sample_atomicfile: phony bin/sample_atomicfile
build middleware/persistency/sample$:sample_shareconfig: phony bin/sample_shareconfig
build middleware/persistency/sample$:sample_systemprop: phony bin/sample_systemprop
build middleware/persistency/test$:classtest_persistency: phony bin/classtest_persistency
build middleware/persistency/test$:persistency_test: phony obj/middleware/persistency/test/persistency_test.stamp
build middleware/persistency/test$:shareconfigcrc: phony bin/shareconfigcrc
build middleware/persistency/test$:shareconfigstress: phony bin/shareconfigstress
build middleware/persistency/test$:unittest_persistency: phony bin/unittest_persistency
build middleware/persistency/utils$:getshareconfig: phony bin/getshareconfig
build middleware/persistency/utils$:setshareconfig: phony bin/setshareconfig
build middleware/persistency/utils$:utils: phony obj/middleware/persistency/utils/utils.stamp
build middleware/persistency/utils: phony obj/middleware/persistency/utils/utils.stamp
build middleware/system/core/libcppbase$:cppbase: phony ./libcppbase.so
build middleware/system/core/libcppbase/demo$:sample_threadpool: phony bin/sample_threadpool
build middleware/system/core/libmessage$:message: phony ./libmessage.so
build middleware/system/core/libmessage/demo$:sample_message: phony bin/sample_message
build middleware/system/tools/filemonitor$:file_monitor: phony bin/file_monitor
build middleware/system/tools/scantree$:scantree: phony bin/scantree
build middleware/system/tools/scantree: phony bin/scantree
build middleware/tombstone$:tombstone_group: phony obj/middleware/tombstone/tombstone_group.stamp
build middleware/tombstone/sample$:sample_tombstone: phony bin/sample_tombstone
build middleware/tombstone/src/backtrace$:backtrace_tombstone: phony ./libbacktrace_tombstone.a
build middleware/tombstone/src/backtrace/demangle$:demangle_tombstone: phony ./libdemangle_tombstone.a
build middleware/tombstone/src/base$:base_tombstone: phony ./libbase_tombstone.a
build middleware/tombstone/src/debuggerd$:test: phony bin/test
build middleware/tombstone/src/debuggerd$:tombstone: phony bin/tombstone
build middleware/tombstone/src/debuggerd$:tombstone_client: phony ./libtombstone_client.so
build middleware/tombstone/src/libcutils$:libcutils_tombstone: phony ./libcutils_tombstone.a
build middleware/tombstone/src/procinfo$:procinfo_tombstone: phony ./libprocinfo_tombstone.a
build middleware/tombstone/src/unwindstack$:unwindstack_tombstone: phony ./libunwindstack_tombstone.a
build middleware/tombstone/src/unwindstack/lzma$:lzma_tombstone: phony ./liblzma_tombstone.a
build middleware/tombstone/utils$:backtrace_tool: phony bin/backtrace_tool

build all: phony $
    obj/build/minieye/all.stamp $
    bin/PairTest $
    bin/PubSubTest $
    bin/ReqRepTest $
    bin/SurveyTest $
    ./libnnmsg.so $
    obj/middleware/communication/nanomsg/library_nanomsg_group.stamp $
    obj/middleware/daemon/daemon_group.stamp $
    obj/middleware/daemon/sample/daemon_sample.stamp $
    bin/sample_em $
    ./libdaemon.so $
    ./libproperty.so $
    bin/daemon $
    obj/middleware/daemon/test/daemon_test.stamp $
    bin/stress_test_daemon $
    bin/unittest_daemon $
    bin/unittest_daemon_sh $
    bin/dumprc $
    bin/getsystemprop $
    bin/setsystemprop $
    bin/startrc $
    bin/stoprc $
    obj/middleware/daemon/utils/utils.stamp $
    bin/dumpsys $
    obj/middleware/dumpsys/dumpsys_group.stamp $
    ./libdumpsys_interface.so $
    bin/sample_dumpsys $
    obj/middleware/logd/logd_group.stamp $
    bin/sample_logd $
    ./libbase_logd.a $
    ./libcutils_logd.a $
    ./liblogd.so $
    ./liblogd_static.a $
    ./libpackagelistparser_logd.a $
    ./libsysutils_logd.a $
    bin/logdcat $
    bin/logd $
    ./libpcre_logd.a $
    ./libpcrecpp_logd.a $
    ./libmlog.so $
    ./libmlog_static.a $
    obj/middleware/mlog/mlog_group.stamp $
    bin/mlogcat $
    bin/mlogdeobf $
    obj/middleware/mlog/sample/mlog_sample.stamp $
    bin/mlogsend $
    bin/sample_mlog $
    obj/middleware/mlog/test/mlog_test.stamp $
    bin/performance $
    bin/unittest_mlog $
    ./libpersistency.so $
    ./libpersistency_common.so $
    bin/persistency $
    obj/middleware/persistency/persistency_group.stamp $
    obj/middleware/persistency/sample/persistency_sample.stamp $
    bin/sample_atomicfile $
    bin/sample_shareconfig $
    bin/sample_systemprop $
    bin/classtest_persistency $
    obj/middleware/persistency/test/persistency_test.stamp $
    bin/shareconfigcrc $
    bin/shareconfigstress $
    bin/unittest_persistency $
    bin/getshareconfig $
    bin/setshareconfig $
    obj/middleware/persistency/utils/utils.stamp $
    ./libcppbase.so $
    bin/sample_threadpool $
    ./libmessage.so $
    bin/sample_message $
    bin/file_monitor $
    bin/scantree $
    obj/middleware/tombstone/tombstone_group.stamp $
    bin/sample_tombstone $
    ./libbacktrace_tombstone.a $
    ./libdemangle_tombstone.a $
    ./libbase_tombstone.a $
    bin/test $
    bin/tombstone $
    ./libtombstone_client.so $
    ./libcutils_tombstone.a $
    ./libprocinfo_tombstone.a $
    ./libunwindstack_tombstone.a $
    ./liblzma_tombstone.a $
    bin/backtrace_tool

default all
