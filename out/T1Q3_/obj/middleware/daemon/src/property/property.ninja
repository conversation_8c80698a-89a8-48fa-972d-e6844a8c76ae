defines =
include_dirs = -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -fPIC
cflags_cc = -fpermissive -std=c++11 -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -fPIC
target_output_name = libproperty

build obj/middleware/daemon/src/property/libproperty.properties.o: cxx ../../middleware/daemon/src/property/properties.cpp
build obj/middleware/daemon/src/property/libproperty.system_properties.o: cxx ../../middleware/daemon/src/property/system_properties.cpp
build obj/middleware/daemon/src/property/libproperty.nn_sub.o: cxx ../../middleware/daemon/src/property/nn_sub.cpp

build ./libproperty.so: solink obj/middleware/daemon/src/property/libproperty.properties.o obj/middleware/daemon/src/property/libproperty.system_properties.o obj/middleware/daemon/src/property/libproperty.nn_sub.o
  ldflags = -L/home/<USER>/mine/out/T1Q3_ -Wl,-rpath-link=/home/<USER>/mine/out/T1Q3_ -lpthread --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -lnanomsg
  libs =
  output_extension = .so
  output_dir = .
