defines =
include_dirs = -I../../middleware/daemon/include -I../../middleware/mlog/include -I../../middleware/daemon/src/property
cflags = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -fPIE
cflags_cc = -Wall -Wextra -Wno-unused-parameter -fno-common -DCROSS_PLAT_64 -O3 -DNDEBUG --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -fPIE
target_output_name = sample_em

build obj/middleware/daemon/sample/sample_em.sample_em.o: cxx ../../middleware/daemon/sample/sample_em.cpp

build bin/sample_em: link obj/middleware/daemon/sample/sample_em.sample_em.o ./libdaemon.so ./libmlog.so ./libproperty.so
  ldflags = -L/home/<USER>/mine/out/T1Q3_ -Wl,-rpath-link=/home/<USER>/mine/out/T1Q3_ -lpthread --sysroot=/home/<USER>/mine/platform/T1Q3/build/sysroot -lnanomsg
  libs =
  output_extension = 
  output_dir = bin
