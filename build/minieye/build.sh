#!/bin/bash

usage() {
    echo
    echo "*************************************************"
    echo "./build.sh                    : build all"
    echo "./build.sh <options>          : build options"
    echo "options:                      :"
    echo "    -h                        : help"
    echo "    -c                        : clean"
    echo "    -v                        : with version output"
    echo "    -m [module]               : build module"
    echo "    -a [product file folder]  : build all product: match all product*.json"
    echo "    -t                        : build release/debug version"
    echo "examples:                     :"
    echo "     ./build.sh -c"
    echo "     ./build.sh -m applications/sensor_abstraction/chassis:chassis_group -v"
    echo "*************************************************"
    echo
}


build_setup() {
    PLATFORM_NAME=$1
    BUILD_SETUP=`cat platform/$PLATFORM_NAME/build/platform.json| ./build/minieye/tools/jq '.build_setup' -r`
    if [ "${BUILD_SETUP}x" != x ]; then
        BUILD_SETUP_FILE="platform/$PLATFORM_NAME/build/$BUILD_SETUP"
        if [[ -x $BUILD_SETUP_FILE ]]; then
            $BUILD_SETUP_FILE
        fi
    fi
}


build() {
    PLATFORM=`cat product.json | ./build/minieye/tools/jq '.platform' -r`
    OUT_DIR="out/""$PLATFORM""_""$BUILD_TYPE"

    build_setup $PLATFORM

    BUILD_CMD="BUILD"
    if [ -n "$clean" ]; then
        BUILD_CMD="CLEAN"
    fi

    if [[ -x $PRE_BUILD_FILE ]]; then
        $PRE_BUILD_FILE $PLATFORM $BUILD_TYPE $OUT_DIR $BUILD_CMD $module
        # 检查 pre_build.sh 的退出状态
        if [ $? -eq 1 ]; then
            echo "pre_build.sh failed with exit code 1"
            exit 1  # 退出 build.sh，表示失败
        else
            echo "pre_build.sh executed successfully"
            # 继续执行其他操作
        fi
    fi

    $GN_TOOL gen $OUT_DIR --root=. --dotfile=build/minieye/.gn --args="BUILD_TYPE_ARG=\"$BUILD_TYPE\""
    echo "$GN_TOOL gen $OUT_DIR --root=. --dotfile=build/minieye/.gn"

    if [ ! -f "$OUT_DIR/build.ninja" ] || [ ! -f "$OUT_DIR/toolchain.ninja" ]; then
        echo "you must build all first !"
        exit 1
    fi


    if [ -n "$clean" ]; then
        if [ -n "$module" ]; then
            echo "$NINJA_TOOL -t clean $module $version"
            cd $OUT_DIR
            $NINJA_TOOL -t clean $module $version
        else
            echo "rm $OUT_DIR/* -rf"
            rm $OUT_DIR/* -rf
        fi
    else
        echo "$NINJA_TOOL -C $OUT_DIR $module $version"
        $NINJA_TOOL -C $OUT_DIR $module $version
        if [ $? -ne 0 ]; then
            echo "[$module] module build failed!!!"
            exit 1
        fi
    fi

    if [[ -x $POST_BUILD_FILE ]]; then
        $POST_BUILD_FILE $PLATFORM $BUILD_TYPE $OUT_DIR $BUILD_CMD $module
    fi
}

cleanup_for_build_all_product() {
    if [ -e "product.json" ]; then
        echo -e "\033[31mrm ./product.json for clean up\033[0m"
        rm product.json
        exit
    fi
}

CURRENT_DIR=$(cd `dirname $0`; pwd)
BUILD_TOOLS_DIR=$CURRENT_DIR/build/minieye/tools
GN_TOOL=$BUILD_TOOLS_DIR/gn
NINJA_TOOL=$BUILD_TOOLS_DIR/ninja
JQ_TOOL=$BUILD_TOOLS_DIR/jq
PRE_BUILD_FILE=$CURRENT_DIR/pre_build.sh
POST_BUILD_FILE=$CURRENT_DIR/post_build.sh
BUILD_TYPE=`cat product.json | ./build/minieye/tools/jq '.build_type' -r`

while getopts hvm:ca:t: opt
do 
	case "${opt}" in
        h) help=true;;
        v) 
            version=-v;
            set -x;
            ;;
        m) module=${OPTARG};;
        c) clean=true;;
        a) productfile_folder=${OPTARG};;
        t)
            BUILD_TYPE="${OPTARG}";
            if [ "$BUILD_TYPE" == "d" ] ; then
                BUILD_TYPE="debug";
            fi
            if [ "$BUILD_TYPE" == "r" ] ; then
                BUILD_TYPE="release";
            fi
            ;;
	esac
done

if [ -n "$help" ]; then
    usage
    exit 0;
fi

usage


if [ -n "$productfile_folder" ]; then
    if [ -e "product.json" ]; then
        echo -e "\033[31m./product.json exists, not support build all product\033[0m"
        exit 1
    else
        files="$(find $productfile_folder -name "product*.json")"
        file_list=($files)
        if [ -n "$file_list" ]; then
            # 循环
            trap cleanup_for_build_all_product ERR EXIT INT
            for file in "${file_list[@]}"
            do
                cp $file product.json
                build
                rm product.json
            done
        else
            echo -e "\033[31mproduct*.json file not found in ./$productfile_folder\033[0m"
            exit 1
        fi
    fi
else
    build
fi




