<?xml version="1.0" encoding="UTF-8"?>
<manifest>

    <remote fetch="ssh://********************/emb_basetech/gn" name="emb_basetech"/>
    <remote fetch="ssh://********************/emb_basetech/middleware" name="middleware"/>
    <default remote="emb_basetech" revision="master" sync-j="8" />

    <!-- 编译仓库 -->  
    <project name="build_minieye" path="build/minieye" remote="emb_basetech"  revision="master">
        <linkfile dest="build.sh" src="build.sh" />
    </project>

    <!-- platform_build -->
    <project name="tda4_platform_build" path="platform/T1Q3/build" remote="emb_basetech" revision="t1q3"/>
    <project name="j3_platform_build" path="platform/J3/build" remote="emb_basetech" revision="master"/>
    <project name="ubuntu_platform_build" path="platform/Ubuntu/build" remote="emb_basetech" revision="master"/>
    <project name="j6e_platform_build" path="platform/J6E/build" remote="emb_basetech" revision="master"/>
    <project name="ssc379g_platform_build" path="platform/SSC379G/build" remote="emb_basetech" revision="master"/>
    <project name="oax8000_platform_build" path="platform/OAX8000/build" remote="emb_basetech" revision="master"/>

    <!-- platform_minieye_sdk -->
    <project name="tda4_platform_minieye_sdk" path="platform/T1Q3/minieye_sdk" remote="emb_basetech" revision="t1q3"/>
    <project name="j3_platform_minieye_sdk" path="platform/J3/minieye_sdk" remote="emb_basetech" revision="master"/>


    <!-- middleware -->
    <project name="mlog" path="middleware/mlog" remote="middleware" revision="master"/>
    <project name="daemon" path="middleware/daemon" remote="middleware" revision="master"/>
    <project name="persistency" path="middleware/persistency" remote="middleware" revision="master"/>
    <project name="system_core" path="middleware/system/core" remote="middleware" revision="master"/>
    <project name="system_tools" path="middleware/system/tools" remote="middleware" revision="master"/>
    <project name="tombstone" path="middleware/tombstone" remote="middleware" revision="master"/>
    <project name="dumpsys" path="middleware/dumpsys" remote="middleware" revision="master"/>
    <project name="communication" path="middleware/communication" remote="middleware" revision="master"/>
    <project name="logd" path="middleware/logd" remote="middleware" revision="master"/>

    <!-- app -->
    <project name="product" path="product" remote="middleware" revision="master"/>




</manifest>
