[core]
	repositoryFormatVersion = 1
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "origin"]
	url = ********************:emb_basetech/middleware/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[manifest]
	platform = auto
[extensions]
	preciousObjects = true
[branch "default"]
	remote = origin
	merge = refs/heads/master
[repo]
	existingprojectcount = 0
	newprojectcount = 19
[repo "syncstate.main"]
	synctime = 2025-08-12T03:56:51.715328+00:00
	version = 1
[repo "syncstate.sys"]
	argv = ['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']
[repo "syncstate.options"]
	jobs = 8
	outermanifest = true
	jobsnetwork = 8
	jobscheckout = 8
	mpupdate = true
	interleaved = true
	currentbranchonly = true
	clonebundle = true
	retryfetches = 0
	prune = true
	repoverify = true
	quiet = false
	verbose = false
[repo "syncstate.remote.origin"]
	url = ********************:emb_basetech/middleware/manifest.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[repo "syncstate.branch.default"]
	remote = origin
	merge = refs/heads/master
[repo "syncstate.repo"]
	existingprojectcount = 0
	newprojectcount = 19
