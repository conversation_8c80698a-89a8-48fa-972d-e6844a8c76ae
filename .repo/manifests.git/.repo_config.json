{"core.repositoryformatversion": ["1"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.origin.url": ["********************:emb_basetech/middleware/manifest.git"], "remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "manifest.platform": ["auto"], "extensions.preciousobjects": ["true"], "branch.default.remote": ["origin"], "branch.default.merge": ["refs/heads/master"], "repo.existingprojectcount": ["0"], "repo.newprojectcount": ["19"], "repo.syncstate.main.synctime": ["2025-08-12T03:56:51.715328+00:00"], "repo.syncstate.main.version": ["1"], "repo.syncstate.sys.argv": ["['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']"], "repo.syncstate.options.jobs": ["8"], "repo.syncstate.options.outermanifest": ["true"], "repo.syncstate.options.jobsnetwork": ["8"], "repo.syncstate.options.jobscheckout": ["8"], "repo.syncstate.options.mpupdate": ["true"], "repo.syncstate.options.interleaved": ["true"], "repo.syncstate.options.currentbranchonly": ["true"], "repo.syncstate.options.clonebundle": ["true"], "repo.syncstate.options.retryfetches": ["0"], "repo.syncstate.options.prune": ["true"], "repo.syncstate.options.repoverify": ["true"], "repo.syncstate.options.quiet": ["false"], "repo.syncstate.options.verbose": ["false"], "repo.syncstate.remote.origin.url": ["********************:emb_basetech/middleware/manifest.git"], "repo.syncstate.remote.origin.fetch": ["+refs/heads/*:refs/remotes/origin/*"], "repo.syncstate.branch.default.remote": ["origin"], "repo.syncstate.branch.default.merge": ["refs/heads/master"], "repo.syncstate.repo.existingprojectcount": ["0"], "repo.syncstate.repo.newprojectcount": ["19"]}