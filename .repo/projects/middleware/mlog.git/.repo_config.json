{"core.repositoryformatversion": ["0"], "core.filemode": ["true"], "filter.lfs.smudge": ["git-lfs smudge --skip -- %f"], "filter.lfs.process": ["git-lfs filter-process --skip"], "remote.middleware.url": ["ssh://********************/emb_basetech/middleware/mlog"], "remote.middleware.projectname": ["mlog"], "remote.middleware.fetch": ["+refs/heads/*:refs/remotes/middleware/*"], "branch.xjs_dev_D4Q.remote": ["middleware"], "branch.xjs_dev_D4Q.merge": ["refs/heads/xjs_dev_D4Q"], "branch.xjs_dev_D4Q.vscode-merge-base": ["middleware/xjs_dev_D4Q"]}