[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "middleware"]
	url = ssh://********************/emb_basetech/middleware/mlog
	projectname = mlog
	fetch = +refs/heads/*:refs/remotes/middleware/*
[branch "xjs_dev_D4Q"]
	remote = middleware
	merge = refs/heads/xjs_dev_D4Q
	vscode-merge-base = middleware/xjs_dev_D4Q
