[core]
	repositoryformatversion = 0
	filemode = true
[filter "lfs"]
	smudge = git-lfs smudge --skip -- %f
	process = git-lfs filter-process --skip
[remote "emb_basetech"]
	url = ssh://********************/emb_basetech/gn/build_minieye
	projectname = build_minieye
	fetch = +refs/heads/*:refs/remotes/emb_basetech/*
[branch "d2j"]
	remote = emb_basetech
	merge = refs/heads/master
	vscode-merge-base = emb_basetech/master
