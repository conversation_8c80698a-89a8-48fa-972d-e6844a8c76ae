PID: 3228556 START: 1754970825658946816 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, -u, ********************:emb_basetech/middleware/manifest.git, -b, master [sid=repo-20250812T035335Z-P0031438c/repo-20250812T035345Z-P0031438c]

PID: 3228556 START: 1754970825668793600 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=/home/<USER>/mine/.repo/manifests.git
: git init 1>| 2>|

PID: 3228556 END: 1754970825672887296 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'init'] with debug: : export GIT_DIR=/home/<USER>/mine/.repo/manifests.git
: git init 1>| 2>|

PID: 3228556 START: 1754970825673256448 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3228556 END: 1754970825675344640 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3228556 START: 1754970825675840768 :: parsing /home/<USER>/.gitconfig

PID: 3228556 END: 1754970825676287232 :: parsing /home/<USER>/.gitconfig

PID: 3228556 START: 1754970825676715264 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228556 END: 1754970825678323456 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228556 START: 1754970825678616832 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228556 END: 1754970825679895296 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228556 START: 1754970825680098304 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228556 END: 1754970825681197312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228556 START: 1754970825681390592 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.url', '********************:emb_basetech/middleware/manifest.git'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all remote.origin.url ********************:emb_basetech/middleware/manifest.git 1>| 2>|

PID: 3228556 END: 1754970825682458624 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.url', '********************:emb_basetech/middleware/manifest.git'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all remote.origin.url ********************:emb_basetech/middleware/manifest.git 1>| 2>|

PID: 3228556 START: 1754970825682576896 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 3228556 END: 1754970825683603712 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 3228556 START: 1754970825683786496 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 3228556 END: 1754970825684941568 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'manifest.platform', 'auto'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all manifest.platform auto 1>| 2>|

PID: 3228556 START: 1754970825685194496 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 3228556 END: 1754970825686490112 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'core.repositoryFormatVersion', '1'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all core.repositoryFormatVersion 1 1>| 2>|

PID: 3228556 START: 1754970825686641408 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 3228556 END: 1754970825687772416 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'extensions.preciousObjects', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all extensions.preciousObjects true 1>| 2>|

PID: 3228556 START: 1754970825688079872 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/origin/master', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/master:refs/remotes/origin/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228556 END: 1754970826430741504 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/origin/master', '+refs/tags/*:refs/tags/*'] with debug: : export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/master:refs/remotes/origin/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228556 START: 1754970826431046400 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228556 END: 1754970826432580864 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228556 START: 1754970826433077504 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/master^0'] with debug: : git rev-parse --verify refs/remotes/origin/master^0 1>| 2>|

PID: 3228556 END: 1754970826434568192 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--verify', 'refs/remotes/origin/master^0'] with debug: : git rev-parse --verify refs/remotes/origin/master^0 1>| 2>|

PID: 3228556 START: 1754970826434975232 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '280f0f20107d718ab07a7a5b1f8e70f531ac5320'] with debug: 
: cd /home/<USER>/mine/.repo/manifests
: git update-ref --no-deref HEAD 280f0f20107d718ab07a7a5b1f8e70f531ac5320 1>| 2>|

PID: 3228556 END: 1754970826436814848 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', '--no-deref', 'HEAD', '280f0f20107d718ab07a7a5b1f8e70f531ac5320'] with debug: 
: cd /home/<USER>/mine/.repo/manifests
: git update-ref --no-deref HEAD 280f0f20107d718ab07a7a5b1f8e70f531ac5320 1>| 2>|

PID: 3228556 START: 1754970826437181952 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228556 END: 1754970826439063040 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228556 START: 1754970826439145216 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 END: 1754970826439550720 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 START: 1754970826439797504 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 END: 1754970826441177088 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 START: 1754970826441409792 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 END: 1754970826442956288 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 START: 1754970826443158528 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'checkout', '-q', '280f0f20107d718ab07a7a5b1f8e70f531ac5320', '--'] with debug: : git checkout -q 280f0f20107d718ab07a7a5b1f8e70f531ac5320 -- 2>|

PID: 3228556 END: 1754970826444933376 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'checkout', '-q', '280f0f20107d718ab07a7a5b1f8e70f531ac5320', '--'] with debug: : git checkout -q 280f0f20107d718ab07a7a5b1f8e70f531ac5320 -- 2>|

PID: 3228556 START: 1754970826445131776 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 3228556 END: 1754970826446437888 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', '-d', 'refs/heads/default'] with debug: : git update-ref -d refs/heads/default 1>| 2>|

PID: 3228556 START: 1754970826446613504 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 END: 1754970826446666496 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 START: 1754970826446796288 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 END: 1754970826448072960 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 START: 1754970826448247296 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 END: 1754970826449520640 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 START: 1754970826449722112 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 END: 1754970826450976512 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 START: 1754970826451122432 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 END: 1754970826452441856 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228556 START: 1754970826452559616 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 END: 1754970826452652288 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228556 START: 1754970826452912128 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '280f0f20107d718ab07a7a5b1f8e70f531ac5320'] with debug: : git update-ref refs/heads/default 280f0f20107d718ab07a7a5b1f8e70f531ac5320 1>| 2>|

PID: 3228556 END: 1754970826456298496 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'update-ref', 'refs/heads/default', '280f0f20107d718ab07a7a5b1f8e70f531ac5320'] with debug: : git update-ref refs/heads/default 280f0f20107d718ab07a7a5b1f8e70f531ac5320 1>| 2>|

PID: 3228556 START: 1754970826456444672 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 3228556 END: 1754970826458355968 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'symbolic-ref', 'HEAD', 'refs/heads/default'] with debug: : git symbolic-ref HEAD refs/heads/default 1>| 2>|

PID: 3228556 START: 1754970826458593280 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 END: 1754970826459848704 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228556 START: 1754970826463897344 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3228556 END: 1754970826465129728 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3228556 END: 1754970826465219584 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: init, -u, ********************:emb_basetech/middleware/manifest.git, -b, master [sid=repo-20250812T035335Z-P0031438c/repo-20250812T035345Z-P0031438c]

PID: 3228746 START: 1754970826634042368 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c [sid=repo-20250812T035346Z-P0031444a/repo-20250812T035346Z-P0031444a]

PID: 3228746 START: 1754970826635964928 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970826636964096 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970826637466368 :: parsing /home/<USER>/.gitconfig

PID: 3228746 END: 1754970826637649664 :: parsing /home/<USER>/.gitconfig

PID: 3228746 START: 1754970826643944448 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/mine/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970826645097472 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/mine/.repo/repo
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970826645212416 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970826646239488 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/repo/.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/repo/.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970826646986240 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970826649480192 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970826649988608 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970826652488704 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970826652994048 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970826656067072 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970826659629824 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/origin/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/master:refs/remotes/origin/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228746 END: 1754970827325337344 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'fetch', '--quiet', 'origin', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/origin/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/manifests.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/manifests.git/objects
: git fetch --quiet origin --recurse-submodules=no --tags +refs/heads/master:refs/remotes/origin/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228746 START: 1754970827325588992 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 END: 1754970827326323456 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 START: 1754970827326732032 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/master', 'refs/remotes/m/master', 'refs/remotes/origin/master'] with debug: : git symbolic-ref -m manifest set to refs/heads/master refs/remotes/m/master refs/remotes/origin/master 1>| 2>|

PID: 3228746 END: 1754970827329990144 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'symbolic-ref', '-m', 'manifest set to refs/heads/master', 'refs/remotes/m/master', 'refs/remotes/origin/master'] with debug: : git symbolic-ref -m manifest set to refs/heads/master refs/remotes/m/master refs/remotes/origin/master 1>| 2>|

PID: 3228746 START: 1754970827330192128 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 END: 1754970827330300160 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 START: 1754970827330349824 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 END: 1754970827330871808 :: load refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 START: 1754970827330969088 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 END: 1754970827331044608 :: scan refs /home/<USER>/mine/.repo/manifests.git

PID: 3228746 START: 1754970827331217408 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970827332625664 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970827333581824 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 3228746 END: 1754970827335533056 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.existingprojectcount 0 1>| 2>|

PID: 3228746 START: 1754970827335831296 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '19'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 19 1>| 2>|

PID: 3228746 END: 1754970827343758336 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.newprojectcount', '19'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.newprojectcount 19 1>| 2>|

PID: 3228797 START: 1754970827389350144 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/build_minieye.git
: git init 1>| 2>|

PID: 3228798 START: 1754970827389469696 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/communication.git
: git init 1>| 2>|

PID: 3228800 START: 1754970827391427840 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/daemon.git
: git init 1>| 2>|

PID: 3228798 END: 1754970827392282368 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/communication.git
: git init 1>| 2>|

PID: 3228797 END: 1754970827392304896 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/build_minieye.git
: git init 1>| 2>|

PID: 3228800 END: 1754970827393111808 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/daemon.git
: git init 1>| 2>|

PID: 3228798 START: 1754970827393513216 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --null --list 1>| 2>|

PID: 3228802 START: 1754970827393545728 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/logd.git
: git init 1>| 2>|

PID: 3228805 START: 1754970827393656320 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/dumpsys.git
: git init 1>| 2>|

PID: 3228798 END: 1754970827394280704 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --null --list 1>| 2>|

PID: 3228812 START: 1754970827394405120 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/system_core.git
: git init 1>| 2>|

PID: 3228809 START: 1754970827394389504 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/mlog.git
: git init 1>| 2>|

PID: 3228817 START: 1754970827394434816 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/persistency.git
: git init 1>| 2>|

PID: 3228798 START: 1754970827394485504 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228797 START: 1754970827394529024 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --null --list 1>| 2>|

PID: 3228798 END: 1754970827395248896 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 START: 1754970827395322880 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --null --list 1>| 2>|

PID: 3228798 START: 1754970827395371520 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228797 END: 1754970827395823872 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --null --list 1>| 2>|

PID: 3228812 END: 1754970827395856640 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/system_core.git
: git init 1>| 2>|

PID: 3228802 END: 1754970827396055808 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/logd.git
: git init 1>| 2>|

PID: 3228798 END: 1754970827396173568 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 END: 1754970827396191488 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --null --list 1>| 2>|

PID: 3228797 START: 1754970827396231424 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 START: 1754970827396275968 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 END: 1754970827396280320 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/dumpsys.git
: git init 1>| 2>|

PID: 3228809 END: 1754970827396281344 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/mlog.git
: git init 1>| 2>|

PID: 3228800 START: 1754970827396382208 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228817 END: 1754970827397046784 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/persistency.git
: git init 1>| 2>|

PID: 3228798 END: 1754970827397084160 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228797 END: 1754970827397164544 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 END: 1754970827397209600 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 START: 1754970827397311488 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/communication'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/communication 1>| 2>|

PID: 3228797 START: 1754970827397327616 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 START: 1754970827397371392 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228809 START: 1754970827397659904 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3228812 START: 1754970827398066432 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --null --list 1>| 2>|

PID: 3228798 END: 1754970827398194688 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/communication'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/communication 1>| 2>|

PID: 3228797 END: 1754970827398237440 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 END: 1754970827398262272 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228798 START: 1754970827398280704 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'communication'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.projectname communication 1>| 2>|

PID: 3228797 START: 1754970827398408448 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228800 START: 1754970827398420224 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 START: 1754970827398506496 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --null --list 1>| 2>|

PID: 3228805 START: 1754970827398704896 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --null --list 1>| 2>|

PID: 3228809 END: 1754970827398833408 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3228812 END: 1754970827398920960 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --null --list 1>| 2>|

PID: 3228798 END: 1754970827399097088 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'communication'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.projectname communication 1>| 2>|

PID: 3228809 START: 1754970827399171072 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 START: 1754970827399189248 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228800 END: 1754970827399230976 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228812 START: 1754970827399266560 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228797 END: 1754970827399308288 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 START: 1754970827399403008 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --null --list 1>| 2>|

PID: 3228800 START: 1754970827399460864 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/daemon'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/daemon 1>| 2>|

PID: 3228797 START: 1754970827399531008 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/build_minieye'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/build_minieye 1>| 2>|

PID: 3228805 END: 1754970827399542784 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970827399822080 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --null --list 1>| 2>|

PID: 3228805 START: 1754970827399895040 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 END: 1754970827399922944 :git command /home/<USER>/mine/.repo/project-objects/communication.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228809 END: 1754970827400077056 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228812 END: 1754970827400091392 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228812 START: 1754970827400191488 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 START: 1754970827400180224 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228809 START: 1754970827400236800 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 END: 1754970827400257280 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --null --list 1>| 2>|

PID: 3228817 START: 1754970827400455680 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228805 END: 1754970827400722176 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228805 START: 1754970827400816640 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 END: 1754970827400875264 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/daemon'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/daemon 1>| 2>|

PID: 3228797 END: 1754970827400912896 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/build_minieye'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/build_minieye 1>| 2>|

PID: 3228812 END: 1754970827400980992 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 END: 1754970827401013248 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 START: 1754970827401043200 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'daemon'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.projectname daemon 1>| 2>|

PID: 3228798 START: 1754970827401084672 :Call to ssh (check call): ssh -o ControlPath /tmp/ssh-bqw__xn7/master-%C ******************** -O check

PID: 3228802 START: 1754970827401099776 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228797 START: 1754970827401083392 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'build_minieye'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.projectname build_minieye 1>| 2>|

PID: 3228812 START: 1754970827401160704 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 END: 1754970827401530368 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228809 END: 1754970827401637120 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228805 START: 1754970827401668096 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228809 START: 1754970827401807104 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 END: 1754970827401812992 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 END: 1754970827401909760 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 END: 1754970827401950464 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'daemon'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.projectname daemon 1>| 2>|

PID: 3228802 START: 1754970827401988608 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 START: 1754970827402007552 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 START: 1754970827402110976 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228797 END: 1754970827402412032 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'build_minieye'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.projectname build_minieye 1>| 2>|

PID: 3228805 END: 1754970827402472960 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228812 END: 1754970827402489856 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228797 START: 1754970827402591744 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228805 START: 1754970827402648832 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/dumpsys'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/dumpsys 1>| 2>|

PID: 3228812 START: 1754970827402714624 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/system_core'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/system_core 1>| 2>|

PID: 3228809 END: 1754970827402900992 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228800 END: 1754970827403047936 :git command /home/<USER>/mine/.repo/project-objects/daemon.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228809 START: 1754970827403148800 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/mlog'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/mlog 1>| 2>|

PID: 3228817 END: 1754970827403219456 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 START: 1754970827403324672 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 END: 1754970827403361024 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 END: 1754970827403449600 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/dumpsys'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/dumpsys 1>| 2>|

PID: 3228805 START: 1754970827403544832 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'dumpsys'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.projectname dumpsys 1>| 2>|

PID: 3228802 START: 1754970827403594240 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/logd'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/logd 1>| 2>|

PID: 3228798 END: 1754970827403909632 :Call to ssh (check call): ssh -o ControlPath /tmp/ssh-bqw__xn7/master-%C ******************** -O check

PID: 3228797 END: 1754970827403917568 :git command /home/<USER>/mine/.repo/project-objects/build_minieye.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228812 END: 1754970827404019712 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/system_core'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/system_core 1>| 2>|

PID: 3228812 START: 1754970827404192768 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'system_core'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.projectname system_core 1>| 2>|

PID: 3228817 END: 1754970827404202496 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 END: 1754970827404286720 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'dumpsys'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.projectname dumpsys 1>| 2>|

PID: 3228817 START: 1754970827404333568 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/persistency'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/persistency 1>| 2>|

PID: 3228805 START: 1754970827404380416 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228809 END: 1754970827404623104 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/mlog'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/mlog 1>| 2>|

PID: 3228798 START: 1754970827404654592 :Call to ssh: ssh -M -N -o SetEnv GIT_PROTOCOL=version=2 -o ControlPath /tmp/ssh-bqw__xn7/master-%C ********************

PID: 3228809 START: 1754970827404799232 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'mlog'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.projectname mlog 1>| 2>|

PID: 3228798 END: 1754970827404899840 :Call to ssh: ssh -M -N -o SetEnv GIT_PROTOCOL=version=2 -o ControlPath /tmp/ssh-bqw__xn7/master-%C ********************

PID: 3228802 END: 1754970827404922624 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/logd'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/logd 1>| 2>|

PID: 3228802 START: 1754970827405020160 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'logd'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.projectname logd 1>| 2>|

PID: 3228805 END: 1754970827405125376 :git command /home/<USER>/mine/.repo/project-objects/dumpsys.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228812 END: 1754970827405518592 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'system_core'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.projectname system_core 1>| 2>|

PID: 3228817 END: 1754970827405641216 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/persistency'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/persistency 1>| 2>|

PID: 3228809 END: 1754970827405656320 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'mlog'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.projectname mlog 1>| 2>|

PID: 3228812 START: 1754970827405697024 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228817 START: 1754970827405728000 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'persistency'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.projectname persistency 1>| 2>|

PID: 3228809 START: 1754970827405756672 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228802 END: 1754970827406206720 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'logd'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.projectname logd 1>| 2>|

PID: 3228802 START: 1754970827406374656 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228809 END: 1754970827406931712 :git command /home/<USER>/mine/.repo/project-objects/mlog.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228812 END: 1754970827406955776 :git command /home/<USER>/mine/.repo/project-objects/system_core.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228817 END: 1754970827407031296 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'persistency'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.projectname persistency 1>| 2>|

PID: 3228817 START: 1754970827407197440 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228802 END: 1754970827407604480 :git command /home/<USER>/mine/.repo/project-objects/logd.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228817 END: 1754970827408442624 :git command /home/<USER>/mine/.repo/project-objects/persistency.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228798 START: 1754970828406535936 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/communication.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/communication.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228800 START: 1754970828409331456 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/daemon.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/daemon.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228797 START: 1754970828410194432 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/build/minieye.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/build_minieye.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228805 START: 1754970828411969792 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/dumpsys.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/dumpsys.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228809 START: 1754970828412422400 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/mlog.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/mlog.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228812 START: 1754970828413415936 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/system/core.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/system_core.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 START: 1754970828414167808 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/logd.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/logd.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 START: 1754970828414735104 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/persistency.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/persistency.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228800 END: 1754970828782146816 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/daemon.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/daemon.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228800 START: 1754970828782322432 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228798 END: 1754970828782818560 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/communication.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/communication.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228798 START: 1754970828782959872 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228800 END: 1754970828783494656 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228800 START: 1754970828783571968 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 END: 1754970828783703808 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 START: 1754970828783796224 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228798 END: 1754970828784463360 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228798 START: 1754970828784519424 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228798 END: 1754970828784638976 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228798 START: 1754970828784718848 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228800 END: 1754970828785120512 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228800 START: 1754970828785419008 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228798 END: 1754970828785614848 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228798 START: 1754970828785898752 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228800 END: 1754970828786695424 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228800 START: 1754970828786884864 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'update-ref', '--no-deref', 'HEAD', 'ea5c59f0f7e7a3d5153929c228a68175b709fbc7'] with debug: 
: cd /home/<USER>/mine/middleware/daemon
: git update-ref --no-deref HEAD ea5c59f0f7e7a3d5153929c228a68175b709fbc7 1>| 2>|

PID: 3228812 END: 1754970828787106560 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/system/core.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/system_core.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228798 END: 1754970828787185664 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228812 START: 1754970828787223552 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228798 START: 1754970828787362816 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'update-ref', '--no-deref', 'HEAD', 'dfe8049099606c270c3394bda379f6c8f1c57ba7'] with debug: 
: cd /home/<USER>/mine/middleware/communication
: git update-ref --no-deref HEAD dfe8049099606c270c3394bda379f6c8f1c57ba7 1>| 2>|

PID: 3228798 END: 1754970828788425472 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'update-ref', '--no-deref', 'HEAD', 'dfe8049099606c270c3394bda379f6c8f1c57ba7'] with debug: 
: cd /home/<USER>/mine/middleware/communication
: git update-ref --no-deref HEAD dfe8049099606c270c3394bda379f6c8f1c57ba7 1>| 2>|

PID: 3228812 END: 1754970828788466432 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228800 END: 1754970828788465920 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'update-ref', '--no-deref', 'HEAD', 'ea5c59f0f7e7a3d5153929c228a68175b709fbc7'] with debug: 
: cd /home/<USER>/mine/middleware/daemon
: git update-ref --no-deref HEAD ea5c59f0f7e7a3d5153929c228a68175b709fbc7 1>| 2>|

PID: 3228812 START: 1754970828788524288 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228798 START: 1754970828788566528 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228800 START: 1754970828788612096 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228812 END: 1754970828788645888 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228812 START: 1754970828788730112 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228812 END: 1754970828790060800 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228812 START: 1754970828790327552 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228812 END: 1754970828791660800 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228812 START: 1754970828791849984 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'update-ref', '--no-deref', 'HEAD', '2948df396d6a7e722536466edb2f95a983a5b608'] with debug: 
: cd /home/<USER>/mine/middleware/system/core
: git update-ref --no-deref HEAD 2948df396d6a7e722536466edb2f95a983a5b608 1>| 2>|

PID: 3228812 END: 1754970828793438464 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'update-ref', '--no-deref', 'HEAD', '2948df396d6a7e722536466edb2f95a983a5b608'] with debug: 
: cd /home/<USER>/mine/middleware/system/core
: git update-ref --no-deref HEAD 2948df396d6a7e722536466edb2f95a983a5b608 1>| 2>|

PID: 3228812 START: 1754970828793578752 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228800 END: 1754970828794374144 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228800 START: 1754970828794467072 :: scan refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 END: 1754970828794515200 :: scan refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 START: 1754970828794538752 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 END: 1754970828794793216 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 START: 1754970828794954240 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228800 END: 1754970828796177408 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228800 START: 1754970828796343552 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228800 END: 1754970828797320448 :git command /home/<USER>/mine/.repo/projects/middleware/daemon.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228812 END: 1754970828797624576 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228812 START: 1754970828797716224 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228812 END: 1754970828797764864 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228812 START: 1754970828797789184 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228800 START: 1754970828797845248 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/system_tools.git
: git init 1>| 2>|

PID: 3228798 END: 1754970828797999360 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228812 END: 1754970828798049792 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228746 START: 1754970828798021120 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228798 START: 1754970828798100224 :: scan refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228798 END: 1754970828798153472 :: scan refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228798 START: 1754970828798179072 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228812 START: 1754970828798205184 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754970828798336000 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228798 END: 1754970828798456320 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228746 START: 1754970828798622464 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --null --list 1>| 2>|

PID: 3228798 START: 1754970828798629376 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228812 END: 1754970828799435776 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228800 END: 1754970828799506944 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/system_tools.git
: git init 1>| 2>|

PID: 3228812 START: 1754970828799589888 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 END: 1754970828799894528 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/daemon.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/daemon.git/config --includes --null --list 1>| 2>|

PID: 3228798 END: 1754970828799921408 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228798 START: 1754970828800018944 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970828800248064 :: scan refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228800 START: 1754970828800295424 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970828800311296 :: scan refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3228798 END: 1754970828800737024 :git command /home/<USER>/mine/.repo/projects/middleware/communication.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228812 END: 1754970828800821760 :git command /home/<USER>/mine/.repo/projects/middleware/system/core.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970828801173760 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228798 START: 1754970828801259008 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tombstone.git
: git init 1>| 2>|

PID: 3228800 END: 1754970828801327104 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --null --list 1>| 2>|

PID: 3228800 START: 1754970828801584128 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228746 END: 1754970828801597184 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228812 START: 1754970828801657088 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j3_platform_build.git
: git init 1>| 2>|

PID: 3228746 START: 1754970828801765632 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --null --list 1>| 2>|

PID: 3228798 END: 1754970828802442496 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tombstone.git
: git init 1>| 2>|

PID: 3228746 END: 1754970828802601728 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/communication.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/communication.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970828802718976 :: scan refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228746 END: 1754970828802745344 :: scan refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3228746 START: 1754970828802791168 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228800 END: 1754970828802780672 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228746 END: 1754970828802916352 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228800 START: 1754970828802938880 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228746 START: 1754970828803009280 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --null --list 1>| 2>|

PID: 3228798 START: 1754970828803214592 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --null --list 1>| 2>|

PID: 3228812 END: 1754970828803840000 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j3_platform_build.git
: git init 1>| 2>|

PID: 3228800 END: 1754970828803883520 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228798 END: 1754970828803914752 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --null --list 1>| 2>|

PID: 3228798 START: 1754970828804041216 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 START: 1754970828804053248 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228746 END: 1754970828804077312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/core.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/core.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970828804272128 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228746 END: 1754970828804328960 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3228798 END: 1754970828804795648 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 START: 1754970828804881664 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228812 START: 1754970828805306368 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --null --list 1>| 2>|

PID: 3228800 END: 1754970828805338624 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228800 START: 1754970828805523200 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/system_tools'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/system_tools 1>| 2>|

PID: 3228798 END: 1754970828805878784 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228798 START: 1754970828805959168 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228812 END: 1754970828806518528 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --null --list 1>| 2>|

PID: 3228800 END: 1754970828806758144 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/system_tools'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/system_tools 1>| 2>|

PID: 3228812 START: 1754970828806768640 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228800 START: 1754970828806844416 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'system_tools'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.projectname system_tools 1>| 2>|

PID: 3228798 END: 1754970828807107840 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228798 START: 1754970828807200512 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/tombstone'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/tombstone 1>| 2>|

PID: 3228800 END: 1754970828808009984 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'system_tools'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.projectname system_tools 1>| 2>|

PID: 3228800 START: 1754970828808099328 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228812 END: 1754970828808140544 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228798 END: 1754970828808275200 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/tombstone'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/tombstone 1>| 2>|

PID: 3228812 START: 1754970828808297728 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228798 START: 1754970828808354048 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'tombstone'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.projectname tombstone 1>| 2>|

PID: 3228800 END: 1754970828809113600 :git command /home/<USER>/mine/.repo/project-objects/system_tools.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228798 END: 1754970828809299968 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'tombstone'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.projectname tombstone 1>| 2>|

PID: 3228798 START: 1754970828809385472 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228812 END: 1754970828809522432 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228800 START: 1754970828809613312 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/system/tools.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/system_tools.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228812 START: 1754970828809670144 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228798 END: 1754970828810531328 :git command /home/<USER>/mine/.repo/project-objects/tombstone.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228798 START: 1754970828810847488 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/tombstone.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tombstone.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228812 END: 1754970828810938112 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228812 START: 1754970828811126528 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j3_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j3_platform_build 1>| 2>|

PID: 3228812 END: 1754970828812376064 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j3_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j3_platform_build 1>| 2>|

PID: 3228812 START: 1754970828812538880 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j3_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.projectname j3_platform_build 1>| 2>|

PID: 3228812 END: 1754970828813453312 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j3_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.projectname j3_platform_build 1>| 2>|

PID: 3228812 START: 1754970828813601280 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228812 END: 1754970828814585856 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228812 START: 1754970828815171328 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J3/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j3_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 END: 1754970828819044864 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/persistency.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/persistency.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 START: 1754970828819279616 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 END: 1754970828820911616 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 START: 1754970828821040640 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 END: 1754970828821309952 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 START: 1754970828821479168 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228817 END: 1754970828822946304 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228817 START: 1754970828823437312 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228817 END: 1754970828824896768 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228817 START: 1754970828825088000 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'update-ref', '--no-deref', 'HEAD', 'cb5de098fdcb89551bf0afeefcc10fa145aa66df'] with debug: 
: cd /home/<USER>/mine/middleware/persistency
: git update-ref --no-deref HEAD cb5de098fdcb89551bf0afeefcc10fa145aa66df 1>| 2>|

PID: 3228817 END: 1754970828826424576 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'update-ref', '--no-deref', 'HEAD', 'cb5de098fdcb89551bf0afeefcc10fa145aa66df'] with debug: 
: cd /home/<USER>/mine/middleware/persistency
: git update-ref --no-deref HEAD cb5de098fdcb89551bf0afeefcc10fa145aa66df 1>| 2>|

PID: 3228817 START: 1754970828826560256 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 END: 1754970828834465792 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 START: 1754970828834578944 :: scan refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 END: 1754970828834631680 :: scan refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 START: 1754970828834656000 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 END: 1754970828834926336 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 START: 1754970828835109376 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 END: 1754970828836418048 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 START: 1754970828836587520 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228817 END: 1754970828837847040 :git command /home/<USER>/mine/.repo/projects/middleware/persistency.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970828838578944 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 START: 1754970828838624512 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git
: git init 1>| 2>|

PID: 3228746 END: 1754970828838917376 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228746 START: 1754970828839136000 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970828840421120 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/persistency.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/persistency.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970828840652800 :: scan refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228746 END: 1754970828840712960 :: scan refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3228817 END: 1754970828840834816 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git
: git init 1>| 2>|

PID: 3228817 START: 1754970828842423296 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228817 END: 1754970828843585536 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228817 START: 1754970828843840256 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228817 END: 1754970828845092352 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228817 START: 1754970828845234432 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 END: 1754970828846492416 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 START: 1754970828846638336 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 END: 1754970828847952128 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 START: 1754970828848118272 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j3_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j3_platform_minieye_sdk 1>| 2>|

PID: 3228817 END: 1754970828849371136 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j3_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j3_platform_minieye_sdk 1>| 2>|

PID: 3228817 START: 1754970828849512960 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j3_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.projectname j3_platform_minieye_sdk 1>| 2>|

PID: 3228817 END: 1754970828850801920 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j3_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.projectname j3_platform_minieye_sdk 1>| 2>|

PID: 3228817 START: 1754970828850955520 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228817 END: 1754970828852128000 :git command /home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228817 START: 1754970828852648192 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228805 END: 1754970829156576000 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/dumpsys.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/dumpsys.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228805 START: 1754970829156815360 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228805 END: 1754970829158977792 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228805 START: 1754970829159118848 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 END: 1754970829159423744 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 START: 1754970829159629312 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228805 END: 1754970829161156608 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228805 START: 1754970829161847040 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228805 END: 1754970829163635456 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228805 START: 1754970829163902208 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'update-ref', '--no-deref', 'HEAD', 'dd63692b65c8043445c513edbc2513234c065feb'] with debug: 
: cd /home/<USER>/mine/middleware/dumpsys
: git update-ref --no-deref HEAD dd63692b65c8043445c513edbc2513234c065feb 1>| 2>|

PID: 3228805 END: 1754970829165635072 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'update-ref', '--no-deref', 'HEAD', 'dd63692b65c8043445c513edbc2513234c065feb'] with debug: 
: cd /home/<USER>/mine/middleware/dumpsys
: git update-ref --no-deref HEAD dd63692b65c8043445c513edbc2513234c065feb 1>| 2>|

PID: 3228805 START: 1754970829165816832 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228805 END: 1754970829169960192 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228805 START: 1754970829170034688 :: scan refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 END: 1754970829170081792 :: scan refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 START: 1754970829170100992 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 END: 1754970829170334464 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 START: 1754970829170505472 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228805 END: 1754970829171444992 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228805 START: 1754970829171566592 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228805 END: 1754970829172964608 :git command /home/<USER>/mine/.repo/projects/middleware/dumpsys.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228805 START: 1754970829173800448 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j6e_platform_build.git
: git init 1>| 2>|

PID: 3228746 START: 1754970829174001152 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228746 END: 1754970829174446336 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228746 START: 1754970829174721792 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --null --list 1>| 2>|

PID: 3228805 END: 1754970829176006400 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/j6e_platform_build.git
: git init 1>| 2>|

PID: 3228746 END: 1754970829176685312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970829176918016 :: scan refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228746 END: 1754970829176975360 :: scan refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3228805 START: 1754970829177656320 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --null --list 1>| 2>|

PID: 3228805 END: 1754970829178779904 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --null --list 1>| 2>|

PID: 3228805 START: 1754970829179220992 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228805 END: 1754970829180653312 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228805 START: 1754970829180847360 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228805 END: 1754970829182180352 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228805 START: 1754970829182372864 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 END: 1754970829183692800 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228805 START: 1754970829183853056 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j6e_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j6e_platform_build 1>| 2>|

PID: 3228805 END: 1754970829185296384 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/j6e_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/j6e_platform_build 1>| 2>|

PID: 3228805 START: 1754970829185428224 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j6e_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.projectname j6e_platform_build 1>| 2>|

PID: 3228805 END: 1754970829186621952 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'j6e_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.projectname j6e_platform_build 1>| 2>|

PID: 3228805 START: 1754970829186725632 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228805 END: 1754970829187782400 :git command /home/<USER>/mine/.repo/project-objects/j6e_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228805 START: 1754970829188140032 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J6E/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j6e_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228809 END: 1754970829647092480 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/mlog.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/mlog.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228809 START: 1754970829647458304 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228809 END: 1754970829649523712 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228809 START: 1754970829649652992 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 END: 1754970829649929472 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 START: 1754970829650107648 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228809 END: 1754970829651569920 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228809 START: 1754970829652075008 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228809 END: 1754970829653483776 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228809 START: 1754970829653692672 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'update-ref', '--no-deref', 'HEAD', '89cf437b7f7140e8293bfa81a1273dd8cd15d944'] with debug: 
: cd /home/<USER>/mine/middleware/mlog
: git update-ref --no-deref HEAD 89cf437b7f7140e8293bfa81a1273dd8cd15d944 1>| 2>|

PID: 3228809 END: 1754970829655288064 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'update-ref', '--no-deref', 'HEAD', '89cf437b7f7140e8293bfa81a1273dd8cd15d944'] with debug: 
: cd /home/<USER>/mine/middleware/mlog
: git update-ref --no-deref HEAD 89cf437b7f7140e8293bfa81a1273dd8cd15d944 1>| 2>|

PID: 3228809 START: 1754970829655438592 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228797 END: 1754970829688407296 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/build/minieye.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/build_minieye.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228797 START: 1754970829688686592 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228797 END: 1754970829690441984 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228797 START: 1754970829690584832 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 END: 1754970829690852864 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 START: 1754970829691029248 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228797 END: 1754970829692605696 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228797 START: 1754970829693157632 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228797 END: 1754970829694913024 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228797 START: 1754970829695043840 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'update-ref', '--no-deref', 'HEAD', 'bfd53e924e223afb8fde0e0b17becf8ad9482a13'] with debug: 
: cd /home/<USER>/mine/build/minieye
: git update-ref --no-deref HEAD bfd53e924e223afb8fde0e0b17becf8ad9482a13 1>| 2>|

PID: 3228797 END: 1754970829696622080 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'update-ref', '--no-deref', 'HEAD', 'bfd53e924e223afb8fde0e0b17becf8ad9482a13'] with debug: 
: cd /home/<USER>/mine/build/minieye
: git update-ref --no-deref HEAD bfd53e924e223afb8fde0e0b17becf8ad9482a13 1>| 2>|

PID: 3228797 START: 1754970829696698624 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228797 END: 1754970829721365248 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228797 START: 1754970829721714176 :: scan refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 END: 1754970829721766656 :: scan refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 START: 1754970829721788672 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 END: 1754970829722123776 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 START: 1754970829722395648 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228797 END: 1754970829723896320 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228797 START: 1754970829724075776 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228797 END: 1754970829726905856 :git command /home/<USER>/mine/.repo/projects/build/minieye.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228809 END: 1754970829727920128 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228809 START: 1754970829728171008 :: scan refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 END: 1754970829728314112 :: scan refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 START: 1754970829728374272 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228746 START: 1754970829728436480 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 START: 1754970829728594688 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git
: git init 1>| 2>|

PID: 3228809 END: 1754970829729052416 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228746 END: 1754970829729148416 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228809 START: 1754970829729470720 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754970829729501184 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --null --list 1>| 2>|

PID: 3228809 END: 1754970829731134464 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228809 START: 1754970829731332096 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 END: 1754970829731460864 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/build/minieye.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/build/minieye.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970829731698944 :: scan refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228746 END: 1754970829731760128 :: scan refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3228797 END: 1754970829731984128 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git
: git init 1>| 2>|

PID: 3228809 END: 1754970829732798208 :git command /home/<USER>/mine/.repo/projects/middleware/mlog.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970829733386240 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228746 END: 1754970829733668352 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 START: 1754970829733682688 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git
: git init 1>| 2>|

PID: 3228746 START: 1754970829733855232 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3228797 START: 1754970829733904384 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970829735094272 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3228797 END: 1754970829735147264 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970829735323392 :: scan refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228797 START: 1754970829735374336 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228746 END: 1754970829735382784 :: scan refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3228809 END: 1754970829735930368 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git
: git init 1>| 2>|

PID: 3228797 END: 1754970829736620800 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228797 START: 1754970829736759040 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228809 START: 1754970829737597952 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --null --list 1>| 2>|

PID: 3228797 END: 1754970829737994240 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228797 START: 1754970829738135808 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228809 END: 1754970829738458880 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --null --list 1>| 2>|

PID: 3228809 START: 1754970829738746368 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228797 END: 1754970829739423488 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228797 START: 1754970829739613184 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/oax8000_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/oax8000_platform_build 1>| 2>|

PID: 3228809 END: 1754970829740147968 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228809 START: 1754970829740288256 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228797 END: 1754970829741194240 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/oax8000_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/oax8000_platform_build 1>| 2>|

PID: 3228797 START: 1754970829741339392 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'oax8000_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.projectname oax8000_platform_build 1>| 2>|

PID: 3228809 END: 1754970829741485312 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228809 START: 1754970829741596416 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228797 END: 1754970829742479616 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'oax8000_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.projectname oax8000_platform_build 1>| 2>|

PID: 3228797 START: 1754970829742618624 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228809 END: 1754970829742797056 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228809 START: 1754970829742918144 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/ssc379g_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/ssc379g_platform_build 1>| 2>|

PID: 3228797 END: 1754970829743833088 :git command /home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228809 END: 1754970829744069888 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/ssc379g_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/ssc379g_platform_build 1>| 2>|

PID: 3228809 START: 1754970829744154112 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'ssc379g_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.projectname ssc379g_platform_build 1>| 2>|

PID: 3228797 START: 1754970829744468480 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228809 END: 1754970829745361664 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'ssc379g_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.projectname ssc379g_platform_build 1>| 2>|

PID: 3228809 START: 1754970829745441792 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228809 END: 1754970829746567936 :git command /home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228809 START: 1754970829746882304 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 END: 1754970829831564800 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j3_platform_minieye_sdk.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 START: 1754970829831899136 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 END: 1754970829833545984 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 START: 1754970829833595904 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 END: 1754970829833747712 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 START: 1754970829833850368 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228817 END: 1754970829835200000 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228817 START: 1754970829835673600 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228817 END: 1754970829837134336 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228817 START: 1754970829837302016 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'update-ref', '--no-deref', 'HEAD', 'afe9ee8e6b228dc8852816f25133361a42087a5a'] with debug: 
: cd /home/<USER>/mine/platform/J3/minieye_sdk
: git update-ref --no-deref HEAD afe9ee8e6b228dc8852816f25133361a42087a5a 1>| 2>|

PID: 3228817 END: 1754970829839283456 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'update-ref', '--no-deref', 'HEAD', 'afe9ee8e6b228dc8852816f25133361a42087a5a'] with debug: 
: cd /home/<USER>/mine/platform/J3/minieye_sdk
: git update-ref --no-deref HEAD afe9ee8e6b228dc8852816f25133361a42087a5a 1>| 2>|

PID: 3228817 START: 1754970829839432448 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 END: 1754970829843995648 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 START: 1754970829844075520 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 END: 1754970829844120576 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 START: 1754970829844143360 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 END: 1754970829844391168 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 START: 1754970829844520192 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 END: 1754970829845885952 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 START: 1754970829846268928 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228817 END: 1754970829847962624 :git command /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970829848753664 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 START: 1754970829848840704 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tda4_platform_build.git
: git init 1>| 2>|

PID: 3228746 END: 1754970829849172736 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228746 START: 1754970829849437696 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970829850734080 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970829851052032 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228746 END: 1754970829851112704 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3228817 END: 1754970829851187968 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tda4_platform_build.git
: git init 1>| 2>|

PID: 3228817 START: 1754970829853776896 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --null --list 1>| 2>|

PID: 3228817 END: 1754970829855122432 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --null --list 1>| 2>|

PID: 3228817 START: 1754970829855392256 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228817 END: 1754970829856740608 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228817 START: 1754970829856903680 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 END: 1754970829858115328 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228817 START: 1754970829858251776 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 END: 1754970829859707392 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228817 START: 1754970829859872000 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/tda4_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/tda4_platform_build 1>| 2>|

PID: 3228817 END: 1754970829861230336 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/tda4_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/tda4_platform_build 1>| 2>|

PID: 3228817 START: 1754970829861377024 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'tda4_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.projectname tda4_platform_build 1>| 2>|

PID: 3228817 END: 1754970829862672640 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'tda4_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.projectname tda4_platform_build 1>| 2>|

PID: 3228817 START: 1754970829862815232 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228817 END: 1754970829864282880 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228817 START: 1754970829864717312 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/t1q3:refs/remotes/emb_basetech/t1q3', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tda4_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/t1q3:refs/remotes/emb_basetech/t1q3 +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 END: 1754970831265748736 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/logd.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/logd.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 START: 1754970831266154240 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 END: 1754970831267806976 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 START: 1754970831267925760 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 END: 1754970831268203520 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 START: 1754970831268381952 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228802 END: 1754970831270045184 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228802 START: 1754970831270508288 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228802 END: 1754970831271779584 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228802 START: 1754970831272246784 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'update-ref', '--no-deref', 'HEAD', '664d7c6e5949da7e70a0184f06287d8e099cb0b3'] with debug: 
: cd /home/<USER>/mine/middleware/logd
: git update-ref --no-deref HEAD 664d7c6e5949da7e70a0184f06287d8e099cb0b3 1>| 2>|

PID: 3228802 END: 1754970831273974272 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'update-ref', '--no-deref', 'HEAD', '664d7c6e5949da7e70a0184f06287d8e099cb0b3'] with debug: 
: cd /home/<USER>/mine/middleware/logd
: git update-ref --no-deref HEAD 664d7c6e5949da7e70a0184f06287d8e099cb0b3 1>| 2>|

PID: 3228802 START: 1754970831274193664 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 END: 1754970831317958144 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 START: 1754970831318171136 :: scan refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 END: 1754970831318268160 :: scan refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 START: 1754970831318312192 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 END: 1754970831318797824 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 START: 1754970831319044864 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 END: 1754970831320707840 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 START: 1754970831321146112 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228802 END: 1754970831323400960 :git command /home/<USER>/mine/.repo/projects/middleware/logd.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970831324648704 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 START: 1754970831324832768 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git
: git init 1>| 2>|

PID: 3228746 END: 1754970831325123328 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228746 START: 1754970831325354752 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970831326937344 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/logd.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/logd.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970831327176448 :: scan refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228746 END: 1754970831327235584 :: scan refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3228802 END: 1754970831327288576 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git
: git init 1>| 2>|

PID: 3228802 START: 1754970831328108800 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970831329204992 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228802 START: 1754970831329453824 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 END: 1754970831330717440 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 START: 1754970831330872576 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 END: 1754970831332159744 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 START: 1754970831332290304 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 END: 1754970831334352640 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 START: 1754970831334505984 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/tda4_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/tda4_platform_minieye_sdk 1>| 2>|

PID: 3228802 END: 1754970831335449088 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/tda4_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/tda4_platform_minieye_sdk 1>| 2>|

PID: 3228802 START: 1754970831335573504 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'tda4_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.projectname tda4_platform_minieye_sdk 1>| 2>|

PID: 3228802 END: 1754970831336804608 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'tda4_platform_minieye_sdk'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.projectname tda4_platform_minieye_sdk 1>| 2>|

PID: 3228802 START: 1754970831336932864 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228802 END: 1754970831338105088 :git command /home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228802 START: 1754970831338635520 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/t1q3:refs/remotes/emb_basetech/t1q3', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/t1q3:refs/remotes/emb_basetech/t1q3 +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 END: 1754970832271228416 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/t1q3:refs/remotes/emb_basetech/t1q3', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tda4_platform_minieye_sdk.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/t1q3:refs/remotes/emb_basetech/t1q3 +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 START: 1754970832271492352 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 END: 1754970832273856768 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 START: 1754970832273965824 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 END: 1754970832274152960 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 START: 1754970832274316800 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'symbolic-ref', '-m', 'manifest set to t1q3', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/t1q3'] with debug: : git symbolic-ref -m manifest set to t1q3 refs/remotes/m/master refs/remotes/emb_basetech/t1q3 1>| 2>|

PID: 3228802 END: 1754970832276061696 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'symbolic-ref', '-m', 'manifest set to t1q3', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/t1q3'] with debug: : git symbolic-ref -m manifest set to t1q3 refs/remotes/m/master refs/remotes/emb_basetech/t1q3 1>| 2>|

PID: 3228802 START: 1754970832276474880 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/t1q3^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/t1q3^0 1>| 2>|

PID: 3228802 END: 1754970832278601472 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/t1q3^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/t1q3^0 1>| 2>|

PID: 3228802 START: 1754970832278766592 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'update-ref', '--no-deref', 'HEAD', '6d992620ca3f4ff8dc3ee01183673c18e1bb4268'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/minieye_sdk
: git update-ref --no-deref HEAD 6d992620ca3f4ff8dc3ee01183673c18e1bb4268 1>| 2>|

PID: 3228802 END: 1754970832280156416 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'update-ref', '--no-deref', 'HEAD', '6d992620ca3f4ff8dc3ee01183673c18e1bb4268'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/minieye_sdk
: git update-ref --no-deref HEAD 6d992620ca3f4ff8dc3ee01183673c18e1bb4268 1>| 2>|

PID: 3228802 START: 1754970832280291584 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 END: 1754970832281911040 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 START: 1754970832281971712 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 END: 1754970832282012160 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 START: 1754970832282033152 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 END: 1754970832282221056 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 START: 1754970832282335488 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 END: 1754970832283620352 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 START: 1754970832283784448 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228802 END: 1754970832284999168 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970832286205952 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 START: 1754970832286499072 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git
: git init 1>| 2>|

PID: 3228746 END: 1754970832286679552 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228746 START: 1754970832287046912 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970832288711424 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970832288839680 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git
: git init 1>| 2>|

PID: 3228746 START: 1754970832289041920 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228746 END: 1754970832289107968 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3228802 START: 1754970832291474432 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970832292761856 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --null --list 1>| 2>|

PID: 3228802 START: 1754970832293011968 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 END: 1754970832294224384 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 START: 1754970832294359552 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 END: 1754970832296145664 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 START: 1754970832296277760 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 END: 1754970832297629440 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 START: 1754970832297797632 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/ubuntu_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/ubuntu_platform_build 1>| 2>|

PID: 3228802 END: 1754970832299163904 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.url', 'ssh://********************/emb_basetech/gn/ubuntu_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.url ssh://********************/emb_basetech/gn/ubuntu_platform_build 1>| 2>|

PID: 3228802 START: 1754970832299308032 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'ubuntu_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.projectname ubuntu_platform_build 1>| 2>|

PID: 3228802 END: 1754970832300571136 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.projectname', 'ubuntu_platform_build'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.projectname ubuntu_platform_build 1>| 2>|

PID: 3228802 START: 1754970832300697600 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228802 END: 1754970832301879040 :git command /home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--replace-all', 'remote.emb_basetech.fetch', '+refs/heads/*:refs/remotes/emb_basetech/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --replace-all remote.emb_basetech.fetch +refs/heads/*:refs/remotes/emb_basetech/* 1>| 2>|

PID: 3228802 START: 1754970832302376448 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 END: 1754970834701135104 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/ubuntu_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 START: 1754970834701731840 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 END: 1754970834704807424 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 START: 1754970834704901888 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 END: 1754970834705085440 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 START: 1754970834705222144 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228802 END: 1754970834706604032 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228802 START: 1754970834706845952 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228802 END: 1754970834708170240 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228802 START: 1754970834708345344 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '379178b1f11547d2fd009b2927278e9e90d8eb53'] with debug: 
: cd /home/<USER>/mine/platform/Ubuntu/build
: git update-ref --no-deref HEAD 379178b1f11547d2fd009b2927278e9e90d8eb53 1>| 2>|

PID: 3228802 END: 1754970834709829120 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '379178b1f11547d2fd009b2927278e9e90d8eb53'] with debug: 
: cd /home/<USER>/mine/platform/Ubuntu/build
: git update-ref --no-deref HEAD 379178b1f11547d2fd009b2927278e9e90d8eb53 1>| 2>|

PID: 3228802 START: 1754970834709963264 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 END: 1754970834747410688 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 START: 1754970834747560960 :: scan refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 END: 1754970834747619072 :: scan refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 START: 1754970834747641856 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 END: 1754970834748024320 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 START: 1754970834748222976 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 END: 1754970834750560000 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 START: 1754970834750691328 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228802 END: 1754970834752738048 :git command /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970834753908992 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 START: 1754970834753972224 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/product.git
: git init 1>| 2>|

PID: 3228746 END: 1754970834754384640 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228746 START: 1754970834754697216 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970834756379648 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'init'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/project-objects/product.git
: git init 1>| 2>|

PID: 3228746 END: 1754970834757198336 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970834757429248 :: scan refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228746 END: 1754970834757484544 :: scan refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3228802 START: 1754970834757982720 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --null --list 1>| 2>|

PID: 3228802 END: 1754970834759170560 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --null --list 1>| 2>|

PID: 3228802 START: 1754970834759568640 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 END: 1754970834761167104 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'filter.lfs.smudge', 'git-lfs smudge --skip -- %f'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all filter.lfs.smudge git-lfs smudge --skip -- %f 1>| 2>|

PID: 3228802 START: 1754970834761309952 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 END: 1754970834762613248 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'filter.lfs.process', 'git-lfs filter-process --skip'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all filter.lfs.process git-lfs filter-process --skip 1>| 2>|

PID: 3228802 START: 1754970834762746624 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 END: 1754970834764139264 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--unset-all', 'core.bare'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --unset-all core.bare 1>| 2>|

PID: 3228802 START: 1754970834764421632 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/product'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/product 1>| 2>|

PID: 3228802 END: 1754970834765820160 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.url', 'ssh://********************/emb_basetech/middleware/product'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.url ssh://********************/emb_basetech/middleware/product 1>| 2>|

PID: 3228802 START: 1754970834765969408 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'product'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.projectname product 1>| 2>|

PID: 3228802 END: 1754970834767137280 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.projectname', 'product'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.projectname product 1>| 2>|

PID: 3228802 START: 1754970834767267328 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228802 END: 1754970834768812800 :git command /home/<USER>/mine/.repo/project-objects/product.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--replace-all', 'remote.middleware.fetch', '+refs/heads/*:refs/remotes/middleware/*'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --replace-all remote.middleware.fetch +refs/heads/*:refs/remotes/middleware/* 1>| 2>|

PID: 3228802 START: 1754970834769299456 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/product.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/product.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 END: 1754970835600552192 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/product.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/product.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228802 START: 1754970835601035776 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 END: 1754970835605155584 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228802 START: 1754970835605386240 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 END: 1754970835606059520 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 START: 1754970835606403328 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228802 END: 1754970835609393664 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228802 START: 1754970835609935360 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228802 END: 1754970835611845376 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228802 START: 1754970835612088064 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'update-ref', '--no-deref', 'HEAD', 'c033236a310cad9f44853c4b4064b9d7c4259aaa'] with debug: 
: cd /home/<USER>/mine/product
: git update-ref --no-deref HEAD c033236a310cad9f44853c4b4064b9d7c4259aaa 1>| 2>|

PID: 3228802 END: 1754970835613780480 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'update-ref', '--no-deref', 'HEAD', 'c033236a310cad9f44853c4b4064b9d7c4259aaa'] with debug: 
: cd /home/<USER>/mine/product
: git update-ref --no-deref HEAD c033236a310cad9f44853c4b4064b9d7c4259aaa 1>| 2>|

PID: 3228802 START: 1754970835613929984 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 END: 1754970835615898624 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228802 START: 1754970835615963392 :: scan refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 END: 1754970835615995392 :: scan refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 START: 1754970835616008960 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 END: 1754970835616186880 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228802 START: 1754970835616292096 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 END: 1754970835617459200 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228802 START: 1754970835617894400 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228802 END: 1754970835619495936 :git command /home/<USER>/mine/.repo/projects/product.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970835620238592 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228746 END: 1754970835621023488 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228746 START: 1754970835621486080 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970835623729408 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/product.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/product.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970835624242944 :: scan refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228746 END: 1754970835624317440 :: scan refs /home/<USER>/mine/.repo/projects/product.git

PID: 3228800 END: 1754970840761303040 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/system/tools.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/system_tools.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228800 START: 1754970840761566720 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228800 END: 1754970840763219712 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228800 START: 1754970840763298048 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 END: 1754970840763531264 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 START: 1754970840763680000 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228800 END: 1754970840765114368 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228800 START: 1754970840765637120 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228800 END: 1754970840767295488 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228800 START: 1754970840767479808 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'update-ref', '--no-deref', 'HEAD', '96e7a9e632b48726ac76a34ae81b762fcdd0d669'] with debug: 
: cd /home/<USER>/mine/middleware/system/tools
: git update-ref --no-deref HEAD 96e7a9e632b48726ac76a34ae81b762fcdd0d669 1>| 2>|

PID: 3228800 END: 1754970840769224192 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'update-ref', '--no-deref', 'HEAD', '96e7a9e632b48726ac76a34ae81b762fcdd0d669'] with debug: 
: cd /home/<USER>/mine/middleware/system/tools
: git update-ref --no-deref HEAD 96e7a9e632b48726ac76a34ae81b762fcdd0d669 1>| 2>|

PID: 3228800 START: 1754970840769382656 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228800 END: 1754970840913642752 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228800 START: 1754970840913806336 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 END: 1754970840913876992 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 START: 1754970840913960448 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 END: 1754970840914131456 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228800 START: 1754970840914258176 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228800 END: 1754970840917548288 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228800 START: 1754970840917997568 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228800 END: 1754970840919614976 :git command /home/<USER>/mine/.repo/projects/middleware/system/tools.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970840920575744 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228746 END: 1754970840920965888 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228746 START: 1754970840921169408 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970840922754816 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/system/tools.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970840922989568 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228746 END: 1754970840923043072 :: scan refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3228798 END: 1754970844737679360 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'fetch', '--quiet', '--progress', 'middleware', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/middleware/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/middleware/tombstone.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tombstone.git/objects
: git fetch --quiet --progress middleware --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/middleware/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228798 START: 1754970844738096384 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228798 END: 1754970844741037056 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228798 START: 1754970844741111296 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 END: 1754970844741308672 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 START: 1754970844741443584 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228798 END: 1754970844742937856 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/middleware/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/middleware/master 1>| 2>|

PID: 3228798 START: 1754970844743227136 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228798 END: 1754970844744835072 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', '--verify', 'refs/remotes/middleware/master^0'] with debug: : git rev-parse --verify refs/remotes/middleware/master^0 1>| 2>|

PID: 3228798 START: 1754970844745075712 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'update-ref', '--no-deref', 'HEAD', 'b82935f6b767eedb3f03100978c2f96acfbb6910'] with debug: 
: cd /home/<USER>/mine/middleware/tombstone
: git update-ref --no-deref HEAD b82935f6b767eedb3f03100978c2f96acfbb6910 1>| 2>|

PID: 3228798 END: 1754970844746750976 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'update-ref', '--no-deref', 'HEAD', 'b82935f6b767eedb3f03100978c2f96acfbb6910'] with debug: 
: cd /home/<USER>/mine/middleware/tombstone
: git update-ref --no-deref HEAD b82935f6b767eedb3f03100978c2f96acfbb6910 1>| 2>|

PID: 3228798 START: 1754970844746917376 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228798 END: 1754970844787069184 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228798 START: 1754970844787322624 :: scan refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 END: 1754970844787454976 :: scan refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 START: 1754970844787516928 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 END: 1754970844788235520 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228798 START: 1754970844788617216 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228798 END: 1754970844791195392 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228798 START: 1754970844791678464 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228798 END: 1754970844793395712 :git command /home/<USER>/mine/.repo/projects/middleware/tombstone.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970844794228224 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228746 END: 1754970844794565632 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228746 START: 1754970844794755328 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970844796600064 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/tombstone.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970844796852992 :: scan refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228746 END: 1754970844796921856 :: scan refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3228797 END: 1754970887493619456 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/oax8000_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228797 START: 1754970887493784320 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228797 END: 1754970887495341312 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228797 START: 1754970887495381504 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 END: 1754970887495492864 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 START: 1754970887495565568 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228797 END: 1754970887497006848 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228797 START: 1754970887497229824 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228797 END: 1754970887498493184 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228797 START: 1754970887498680320 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'update-ref', '--no-deref', 'HEAD', 'd9dbc7ef09eacd869dd86d0b4084df5a8f615062'] with debug: 
: cd /home/<USER>/mine/platform/OAX8000/build
: git update-ref --no-deref HEAD d9dbc7ef09eacd869dd86d0b4084df5a8f615062 1>| 2>|

PID: 3228797 END: 1754970887501706752 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'update-ref', '--no-deref', 'HEAD', 'd9dbc7ef09eacd869dd86d0b4084df5a8f615062'] with debug: 
: cd /home/<USER>/mine/platform/OAX8000/build
: git update-ref --no-deref HEAD d9dbc7ef09eacd869dd86d0b4084df5a8f615062 1>| 2>|

PID: 3228797 START: 1754970887501912832 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228797 END: 1754970888186540288 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228797 START: 1754970888186823168 :: scan refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 END: 1754970888186996736 :: scan refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 START: 1754970888187043840 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 END: 1754970888187742976 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228797 START: 1754970888188131840 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228797 END: 1754970888191658240 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228797 START: 1754970888191822336 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228797 END: 1754970888193604608 :git command /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970888194852352 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228746 END: 1754970888195692288 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228746 START: 1754970888196151552 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970888197637888 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970888198210560 :: scan refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228746 END: 1754970888198376448 :: scan refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3228809 END: 1754970917857958912 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/ssc379g_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228809 START: 1754970917858324480 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228809 END: 1754970917861349120 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228809 START: 1754970917861468416 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 END: 1754970917861711616 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 START: 1754970917861880832 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228809 END: 1754970917863442688 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228809 START: 1754970917863854080 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228809 END: 1754970917865225216 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228809 START: 1754970917865433856 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '505603a055f740fbd754a7ea4483036e934ed5e3'] with debug: 
: cd /home/<USER>/mine/platform/SSC379G/build
: git update-ref --no-deref HEAD 505603a055f740fbd754a7ea4483036e934ed5e3 1>| 2>|

PID: 3228809 END: 1754970917866952704 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '505603a055f740fbd754a7ea4483036e934ed5e3'] with debug: 
: cd /home/<USER>/mine/platform/SSC379G/build
: git update-ref --no-deref HEAD 505603a055f740fbd754a7ea4483036e934ed5e3 1>| 2>|

PID: 3228809 START: 1754970917867118336 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228809 END: 1754970919519712000 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228809 START: 1754970919519873536 :: scan refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 END: 1754970919519935744 :: scan refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 START: 1754970919519958016 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 END: 1754970919520236288 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228809 START: 1754970919520428800 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228809 END: 1754970919522566656 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228809 START: 1754970919522746624 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228809 END: 1754970919525529856 :git command /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970919527220992 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228746 END: 1754970919527588608 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228746 START: 1754970919527845120 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970919529091584 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970919529425408 :: scan refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228746 END: 1754970919529490432 :: scan refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3228812 END: 1754970932342026240 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J3/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j3_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228812 START: 1754970932342301696 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228812 END: 1754970932344068352 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228812 START: 1754970932344193536 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 END: 1754970932344456448 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 START: 1754970932344634368 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228812 END: 1754970932346100224 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228812 START: 1754970932346507008 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228812 END: 1754970932348138496 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228812 START: 1754970932348250368 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '32b39122b258ff51885756137984506085c7503e'] with debug: 
: cd /home/<USER>/mine/platform/J3/build
: git update-ref --no-deref HEAD 32b39122b258ff51885756137984506085c7503e 1>| 2>|

PID: 3228812 END: 1754970932349949696 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '32b39122b258ff51885756137984506085c7503e'] with debug: 
: cd /home/<USER>/mine/platform/J3/build
: git update-ref --no-deref HEAD 32b39122b258ff51885756137984506085c7503e 1>| 2>|

PID: 3228812 START: 1754970932350031616 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228812 END: 1754970934363471360 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228812 START: 1754970934363624448 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 END: 1754970934363683072 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 START: 1754970934363704576 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 END: 1754970934364007168 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228812 START: 1754970934364202752 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228812 END: 1754970934366300928 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228812 START: 1754970934366464512 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228812 END: 1754970934368130816 :git command /home/<USER>/mine/.repo/projects/platform/J3/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970934368732160 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228746 END: 1754970934369056512 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228746 START: 1754970934369194496 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970934371181056 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J3/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970934371314432 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228746 END: 1754970934371343872 :: scan refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3228817 END: 1754970990397273856 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/t1q3:refs/remotes/emb_basetech/t1q3', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/tda4_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/t1q3:refs/remotes/emb_basetech/t1q3 +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228817 START: 1754970990397756928 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 END: 1754970990399966208 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228817 START: 1754970990400137984 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 END: 1754970990400526592 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 START: 1754970990400813056 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'symbolic-ref', '-m', 'manifest set to t1q3', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/t1q3'] with debug: : git symbolic-ref -m manifest set to t1q3 refs/remotes/m/master refs/remotes/emb_basetech/t1q3 1>| 2>|

PID: 3228817 END: 1754970990402484736 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'symbolic-ref', '-m', 'manifest set to t1q3', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/t1q3'] with debug: : git symbolic-ref -m manifest set to t1q3 refs/remotes/m/master refs/remotes/emb_basetech/t1q3 1>| 2>|

PID: 3228817 START: 1754970990402887424 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/t1q3^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/t1q3^0 1>| 2>|

PID: 3228817 END: 1754970990404839168 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/t1q3^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/t1q3^0 1>| 2>|

PID: 3228817 START: 1754970990405015808 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '8d6561a7de854ca03298d0cb86f4b71900a20b3a'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/build
: git update-ref --no-deref HEAD 8d6561a7de854ca03298d0cb86f4b71900a20b3a 1>| 2>|

PID: 3228817 END: 1754970990406475776 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'update-ref', '--no-deref', 'HEAD', '8d6561a7de854ca03298d0cb86f4b71900a20b3a'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/build
: git update-ref --no-deref HEAD 8d6561a7de854ca03298d0cb86f4b71900a20b3a 1>| 2>|

PID: 3228817 START: 1754970990406550784 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 END: 1754970995464943360 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228817 START: 1754970995465079040 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 END: 1754970995465116160 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 START: 1754970995465126144 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 END: 1754970995465256448 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228817 START: 1754970995465381632 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 END: 1754970995467485696 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228817 START: 1754970995468081408 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228817 END: 1754970995469972480 :git command /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754970995470617856 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228746 END: 1754970995471058688 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228746 START: 1754970995471233792 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754970995472575232 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754970995473114624 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228746 END: 1754970995473263872 :: scan refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3228805 END: 1754971007444534016 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'fetch', '--quiet', '--progress', 'emb_basetech', '--prune', '--recurse-submodules=no', '--tags', '+refs/heads/master:refs/remotes/emb_basetech/master', '+refs/tags/*:refs/tags/*'] with debug: 
: export GIT_DIR=/home/<USER>/mine/.repo/projects/platform/J6E/build.git
: export GIT_OBJECT_DIRECTORY=/home/<USER>/mine/.repo/project-objects/j6e_platform_build.git/objects
: git fetch --quiet --progress emb_basetech --prune --recurse-submodules=no --tags +refs/heads/master:refs/remotes/emb_basetech/master +refs/tags/*:refs/tags/* 1>| 2>&1

PID: 3228805 START: 1754971007444997376 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228805 END: 1754971007448536320 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'pack-refs', '--all', '--prune'] with debug: : git pack-refs --all --prune 1>| 2>|

PID: 3228805 START: 1754971007448615936 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 END: 1754971007448816896 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 START: 1754971007449005056 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228805 END: 1754971007450865920 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'symbolic-ref', '-m', 'manifest set to master', 'refs/remotes/m/master', 'refs/remotes/emb_basetech/master'] with debug: : git symbolic-ref -m manifest set to master refs/remotes/m/master refs/remotes/emb_basetech/master 1>| 2>|

PID: 3228805 START: 1754971007451411968 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228805 END: 1754971007452878848 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', '--verify', 'refs/remotes/emb_basetech/master^0'] with debug: : git rev-parse --verify refs/remotes/emb_basetech/master^0 1>| 2>|

PID: 3228805 START: 1754971007453320960 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'update-ref', '--no-deref', 'HEAD', 'c8ef26455bca6a95edf1bdd418b0e464785ff650'] with debug: 
: cd /home/<USER>/mine/platform/J6E/build
: git update-ref --no-deref HEAD c8ef26455bca6a95edf1bdd418b0e464785ff650 1>| 2>|

PID: 3228805 END: 1754971007456797440 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'update-ref', '--no-deref', 'HEAD', 'c8ef26455bca6a95edf1bdd418b0e464785ff650'] with debug: 
: cd /home/<USER>/mine/platform/J6E/build
: git update-ref --no-deref HEAD c8ef26455bca6a95edf1bdd418b0e464785ff650 1>| 2>|

PID: 3228805 START: 1754971007456898816 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228805 END: 1754971011680966656 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'read-tree', '--reset', '-u', '-v', 'HEAD'] with debug: : git read-tree --reset -u -v HEAD 2>|

PID: 3228805 START: 1754971011681142528 :: scan refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 END: 1754971011681202944 :: scan refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 START: 1754971011681224192 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 END: 1754971011681449984 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228805 START: 1754971011681640960 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228805 END: 1754971011685146624 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228805 START: 1754971011685574144 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228805 END: 1754971011687270656 :git command /home/<USER>/mine/.repo/projects/platform/J6E/build.git ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3228746 START: 1754971011688154368 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228746 END: 1754971011688477696 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228746 START: 1754971011688677888 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 END: 1754971011691358976 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/platform/J6E/build.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config --includes --null --list 1>| 2>|

PID: 3228746 START: 1754971011691955200 :: scan refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228746 END: 1754971011692004864 :: scan refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3228746 START: 1754971011695303936 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 END: 1754971011697170944 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3228746 START: 1754971011711767808 :: parsing /home/<USER>/mine/.repo/projects/build/minieye.git/config

PID: 3228746 END: 1754971011711938816 :: parsing /home/<USER>/mine/.repo/projects/build/minieye.git/config

PID: 3228746 START: 1754971011712006912 :: parsing /home/<USER>/mine/.repo/projects/middleware/communication.git/config

PID: 3228746 END: 1754971011712123136 :: parsing /home/<USER>/mine/.repo/projects/middleware/communication.git/config

PID: 3228746 START: 1754971011712177920 :: parsing /home/<USER>/mine/.repo/projects/middleware/daemon.git/config

PID: 3228746 END: 1754971011712288512 :: parsing /home/<USER>/mine/.repo/projects/middleware/daemon.git/config

PID: 3228746 START: 1754971011712338688 :: parsing /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config

PID: 3228746 END: 1754971011712450048 :: parsing /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config

PID: 3228746 START: 1754971011712496384 :: parsing /home/<USER>/mine/.repo/projects/middleware/logd.git/config

PID: 3228746 END: 1754971011712601600 :: parsing /home/<USER>/mine/.repo/projects/middleware/logd.git/config

PID: 3228746 START: 1754971011712648704 :: parsing /home/<USER>/mine/.repo/projects/middleware/mlog.git/config

PID: 3228746 END: 1754971011712755712 :: parsing /home/<USER>/mine/.repo/projects/middleware/mlog.git/config

PID: 3228746 START: 1754971011712801024 :: parsing /home/<USER>/mine/.repo/projects/middleware/persistency.git/config

PID: 3228746 END: 1754971011713191168 :: parsing /home/<USER>/mine/.repo/projects/middleware/persistency.git/config

PID: 3228746 START: 1754971011713241344 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/core.git/config

PID: 3228746 END: 1754971011713342720 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/core.git/config

PID: 3228746 START: 1754971011713388032 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config

PID: 3228746 END: 1754971011713636864 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config

PID: 3228746 START: 1754971011713683200 :: parsing /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config

PID: 3228746 END: 1754971011713784832 :: parsing /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config

PID: 3228746 START: 1754971011713829376 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/build.git/config

PID: 3228746 END: 1754971011714024448 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/build.git/config

PID: 3228746 START: 1754971011714072832 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config

PID: 3228746 END: 1754971011714175232 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config

PID: 3228746 START: 1754971011714214144 :: parsing /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config

PID: 3228746 END: 1754971011714260736 :: parsing /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config

PID: 3228746 START: 1754971011714303744 :: parsing /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config

PID: 3228746 END: 1754971011714405376 :: parsing /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config

PID: 3228746 START: 1754971011714450688 :: parsing /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config

PID: 3228746 END: 1754971011714552064 :: parsing /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config

PID: 3228746 START: 1754971011714591488 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config

PID: 3228746 END: 1754971011714635008 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config

PID: 3228746 START: 1754971011714675712 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config

PID: 3228746 END: 1754971011714779648 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config

PID: 3228746 START: 1754971011714821888 :: parsing /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config

PID: 3228746 END: 1754971011714924288 :: parsing /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config

PID: 3228746 START: 1754971011714967040 :: parsing /home/<USER>/mine/.repo/projects/product.git/config

PID: 3228746 END: 1754971011715066880 :: parsing /home/<USER>/mine/.repo/projects/product.git/config

PID: 3228746 START: 1754971011715483648 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-12T03:56:51.715328+00:00'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-12T03:56:51.715328+00:00 1>| 2>|

PID: 3228746 END: 1754971011716628224 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.synctime', '2025-08-12T03:56:51.715328+00:00'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.synctime 2025-08-12T03:56:51.715328+00:00 1>| 2>|

PID: 3228746 START: 1754971011716781312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 3228746 END: 1754971011718163456 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.main.version', '1'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.main.version 1 1>| 2>|

PID: 3228746 START: 1754971011718374912 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']"] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.sys.argv ['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c'] 1>| 2>|

PID: 3228746 END: 1754971011719963648 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.sys.argv', "['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c']"] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.sys.argv ['/home/<USER>/mine/.repo/repo/main.py', '--repo-dir=/home/<USER>/mine/.repo', '--wrapper-version=2.54', '--wrapper-path=/usr/bin/repo', '--', 'sync', '-c'] 1>| 2>|

PID: 3228746 START: 1754971011720135680 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobs 8 1>| 2>|

PID: 3228746 END: 1754971011721466112 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobs', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobs 8 1>| 2>|

PID: 3228746 START: 1754971011721609216 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 3228746 END: 1754971011722951424 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.outermanifest', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.outermanifest true 1>| 2>|

PID: 3228746 START: 1754971011723090944 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobsnetwork 8 1>| 2>|

PID: 3228746 END: 1754971011724315136 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobsnetwork', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobsnetwork 8 1>| 2>|

PID: 3228746 START: 1754971011724411136 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 3228746 END: 1754971011725564160 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.jobscheckout', '8'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.jobscheckout 8 1>| 2>|

PID: 3228746 START: 1754971011725659904 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 3228746 END: 1754971011726810112 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.mpupdate', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.mpupdate true 1>| 2>|

PID: 3228746 START: 1754971011726902272 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.interleaved', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.interleaved true 1>| 2>|

PID: 3228746 END: 1754971011727953408 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.interleaved', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.interleaved true 1>| 2>|

PID: 3228746 START: 1754971011728023296 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.currentbranchonly', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.currentbranchonly true 1>| 2>|

PID: 3228746 END: 1754971011729098496 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.currentbranchonly', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.currentbranchonly true 1>| 2>|

PID: 3228746 START: 1754971011729175808 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.clonebundle true 1>| 2>|

PID: 3228746 END: 1754971011730322176 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.clonebundle', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.clonebundle true 1>| 2>|

PID: 3228746 START: 1754971011730401024 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 3228746 END: 1754971011731645952 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.retryfetches', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.retryfetches 0 1>| 2>|

PID: 3228746 START: 1754971011731724032 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 3228746 END: 1754971011732760320 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.prune', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.prune true 1>| 2>|

PID: 3228746 START: 1754971011732839936 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 3228746 END: 1754971011734065920 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.repoverify', 'true'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.repoverify true 1>| 2>|

PID: 3228746 START: 1754971011734147840 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 3228746 END: 1754971011735244544 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.quiet', 'false'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.quiet false 1>| 2>|

PID: 3228746 START: 1754971011735314688 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 3228746 END: 1754971011736621312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.options.verbose', 'false'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.options.verbose false 1>| 2>|

PID: 3228746 START: 1754971011736727552 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', '********************:emb_basetech/middleware/manifest.git'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.url ********************:emb_basetech/middleware/manifest.git 1>| 2>|

PID: 3228746 END: 1754971011737882624 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.url', '********************:emb_basetech/middleware/manifest.git'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.url ********************:emb_basetech/middleware/manifest.git 1>| 2>|

PID: 3228746 START: 1754971011737955328 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 3228746 END: 1754971011739084288 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.remote.origin.fetch', '+refs/heads/*:refs/remotes/origin/*'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.remote.origin.fetch +refs/heads/*:refs/remotes/origin/* 1>| 2>|

PID: 3228746 START: 1754971011739151360 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 3228746 END: 1754971011740290304 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.remote', 'origin'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.remote origin 1>| 2>|

PID: 3228746 START: 1754971011740368128 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/master'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/master 1>| 2>|

PID: 3228746 END: 1754971011741481472 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.branch.default.merge', 'refs/heads/master'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.branch.default.merge refs/heads/master 1>| 2>|

PID: 3228746 START: 1754971011741547776 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 3228746 END: 1754971011742669568 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.existingprojectcount', '0'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.existingprojectcount 0 1>| 2>|

PID: 3228746 START: 1754971011742736384 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '19'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 19 1>| 2>|

PID: 3228746 END: 1754971011743855104 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--replace-all', 'repo.syncstate.repo.newprojectcount', '19'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --replace-all repo.syncstate.repo.newprojectcount 19 1>| 2>|

PID: 3228746 START: 1754971011744429312 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3228746 END: 1754971011745367552 :git command /home/<USER>/mine/.repo/manifests.git ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3228746 END: 1754971011745452544 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: sync, -c [sid=repo-20250812T035346Z-P0031444a/repo-20250812T035346Z-P0031444a]

PID: 3242098 START: 1754971402754472192 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: start, d2j, --all [sid=repo-20250812T040322Z-P00317872/repo-20250812T040322Z-P00317872]

PID: 3242098 START: 1754971402758585088 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3242098 END: 1754971402761434880 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/manifests.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/manifests.git/config --includes --null --list 1>| 2>|

PID: 3242098 START: 1754971402762521088 :: parsing /home/<USER>/.gitconfig

PID: 3242098 END: 1754971402762926592 :: parsing /home/<USER>/.gitconfig

PID: 3242098 START: 1754971402770328832 :git command None ['git', 'check-ref-format', 'heads/d2j'] with debug: : git check-ref-format heads/d2j 2>|

PID: 3242098 END: 1754971402772248832 :git command None ['git', 'check-ref-format', 'heads/d2j'] with debug: : git check-ref-format heads/d2j 2>|

PID: 3242098 START: 1754971402772356864 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/mine/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242098 END: 1754971402774948352 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: : cd /home/<USER>/mine/.repo/manifests
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242106 START: 1754971402789976320 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/communication
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402789976320 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/build/minieye
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242107 START: 1754971402790122240 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/daemon
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242109 START: 1754971402790176256 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/logd
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242108 START: 1754971402790222080 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/dumpsys
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 START: 1754971402790328576 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/mlog
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242112 START: 1754971402790468096 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/system/core
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242111 START: 1754971402790463232 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/persistency
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242106 END: 1754971402791718400 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/communication
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242106 START: 1754971402791952640 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 END: 1754971402791960832 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/build/minieye
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242107 END: 1754971402792151808 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/daemon
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402792185088 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242112 END: 1754971402792189696 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/system/core
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 END: 1754971402792232192 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/mlog
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242108 END: 1754971402792228096 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/dumpsys
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 START: 1754971402792290048 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3242111 END: 1754971402792329728 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/persistency
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242107 START: 1754971402792359424 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242112 START: 1754971402792379648 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242108 START: 1754971402792439552 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242110 END: 1754971402792538112 :: load refs /home/<USER>/mine/.repo/projects/middleware/mlog.git

PID: 3242111 START: 1754971402792535040 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242109 END: 1754971402792566272 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/logd
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 START: 1754971402792710912 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3242109 START: 1754971402792719872 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242106 END: 1754971402793230336 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242106 START: 1754971402793309184 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3242110 END: 1754971402793504768 :git command None ['git', 'config', '--file', '/home/<USER>/mine/.repo/projects/middleware/mlog.git/config', '--includes', '--null', '--list'] with debug: : git config --file /home/<USER>/mine/.repo/projects/middleware/mlog.git/config --includes --null --list 1>| 2>|

PID: 3242105 END: 1754971402793710080 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 END: 1754971402793734656 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 START: 1754971402793765120 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3242107 START: 1754971402793805056 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3242112 END: 1754971402793837312 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242108 END: 1754971402793832704 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242111 END: 1754971402793885696 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242112 START: 1754971402793920512 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3242108 START: 1754971402793917184 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3242110 START: 1754971402793929728 :git command None ['git', 'checkout', '-q', '-b', 'd2j', '89cf437b7f7140e8293bfa81a1273dd8cd15d944'] with debug: : git checkout -q -b d2j 89cf437b7f7140e8293bfa81a1273dd8cd15d944 2>|

PID: 3242111 START: 1754971402793958912 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3242106 END: 1754971402793978368 :: load refs /home/<USER>/mine/.repo/projects/middleware/communication.git

PID: 3242106 START: 1754971402794073856 :: parsing /home/<USER>/mine/.repo/projects/middleware/communication.git/config

PID: 3242105 END: 1754971402794101248 :: load refs /home/<USER>/mine/.repo/projects/build/minieye.git

PID: 3242105 START: 1754971402794154240 :: parsing /home/<USER>/mine/.repo/projects/build/minieye.git/config

PID: 3242109 END: 1754971402794179840 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 END: 1754971402794252288 :: load refs /home/<USER>/mine/.repo/projects/middleware/daemon.git

PID: 3242109 START: 1754971402794255104 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3242105 END: 1754971402794287360 :: parsing /home/<USER>/mine/.repo/projects/build/minieye.git/config

PID: 3242106 END: 1754971402794288128 :: parsing /home/<USER>/mine/.repo/projects/middleware/communication.git/config

PID: 3242107 START: 1754971402794334464 :: parsing /home/<USER>/mine/.repo/projects/middleware/daemon.git/config

PID: 3242105 START: 1754971402794420992 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'bfd53e924e223afb8fde0e0b17becf8ad9482a13'] with debug: : git update-ref refs/heads/d2j bfd53e924e223afb8fde0e0b17becf8ad9482a13 1>| 2>|

PID: 3242108 END: 1754971402794455296 :: load refs /home/<USER>/mine/.repo/projects/middleware/dumpsys.git

PID: 3242107 END: 1754971402794492928 :: parsing /home/<USER>/mine/.repo/projects/middleware/daemon.git/config

PID: 3242112 END: 1754971402794499840 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/core.git

PID: 3242106 START: 1754971402794524160 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'dfe8049099606c270c3394bda379f6c8f1c57ba7'] with debug: : git update-ref refs/heads/d2j dfe8049099606c270c3394bda379f6c8f1c57ba7 1>| 2>|

PID: 3242111 END: 1754971402794526720 :: load refs /home/<USER>/mine/.repo/projects/middleware/persistency.git

PID: 3242108 START: 1754971402794547456 :: parsing /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config

PID: 3242112 START: 1754971402794581248 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/core.git/config

PID: 3242111 START: 1754971402794621696 :: parsing /home/<USER>/mine/.repo/projects/middleware/persistency.git/config

PID: 3242107 START: 1754971402794685696 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'ea5c59f0f7e7a3d5153929c228a68175b709fbc7'] with debug: : git update-ref refs/heads/d2j ea5c59f0f7e7a3d5153929c228a68175b709fbc7 1>| 2>|

PID: 3242112 END: 1754971402794737408 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/core.git/config

PID: 3242108 END: 1754971402794739200 :: parsing /home/<USER>/mine/.repo/projects/middleware/dumpsys.git/config

PID: 3242111 END: 1754971402794810624 :: parsing /home/<USER>/mine/.repo/projects/middleware/persistency.git/config

PID: 3242109 END: 1754971402794818816 :: load refs /home/<USER>/mine/.repo/projects/middleware/logd.git

PID: 3242112 START: 1754971402794885376 :git command None ['git', 'update-ref', 'refs/heads/d2j', '2948df396d6a7e722536466edb2f95a983a5b608'] with debug: : git update-ref refs/heads/d2j 2948df396d6a7e722536466edb2f95a983a5b608 1>| 2>|

PID: 3242109 START: 1754971402794923776 :: parsing /home/<USER>/mine/.repo/projects/middleware/logd.git/config

PID: 3242108 START: 1754971402794944768 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'dd63692b65c8043445c513edbc2513234c065feb'] with debug: : git update-ref refs/heads/d2j dd63692b65c8043445c513edbc2513234c065feb 1>| 2>|

PID: 3242111 START: 1754971402795061760 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'cb5de098fdcb89551bf0afeefcc10fa145aa66df'] with debug: : git update-ref refs/heads/d2j cb5de098fdcb89551bf0afeefcc10fa145aa66df 1>| 2>|

PID: 3242109 END: 1754971402795123968 :: parsing /home/<USER>/mine/.repo/projects/middleware/logd.git/config

PID: 3242109 START: 1754971402795363584 :git command None ['git', 'update-ref', 'refs/heads/d2j', '664d7c6e5949da7e70a0184f06287d8e099cb0b3'] with debug: : git update-ref refs/heads/d2j 664d7c6e5949da7e70a0184f06287d8e099cb0b3 1>| 2>|

PID: 3242105 END: 1754971402795456256 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'bfd53e924e223afb8fde0e0b17becf8ad9482a13'] with debug: : git update-ref refs/heads/d2j bfd53e924e223afb8fde0e0b17becf8ad9482a13 1>| 2>|

PID: 3242105 START: 1754971402795622912 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242112 END: 1754971402796259840 :git command None ['git', 'update-ref', 'refs/heads/d2j', '2948df396d6a7e722536466edb2f95a983a5b608'] with debug: : git update-ref refs/heads/d2j 2948df396d6a7e722536466edb2f95a983a5b608 1>| 2>|

PID: 3242111 END: 1754971402796263168 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'cb5de098fdcb89551bf0afeefcc10fa145aa66df'] with debug: : git update-ref refs/heads/d2j cb5de098fdcb89551bf0afeefcc10fa145aa66df 1>| 2>|

PID: 3242106 END: 1754971402796281600 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'dfe8049099606c270c3394bda379f6c8f1c57ba7'] with debug: : git update-ref refs/heads/d2j dfe8049099606c270c3394bda379f6c8f1c57ba7 1>| 2>|

PID: 3242107 END: 1754971402796304128 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'ea5c59f0f7e7a3d5153929c228a68175b709fbc7'] with debug: : git update-ref refs/heads/d2j ea5c59f0f7e7a3d5153929c228a68175b709fbc7 1>| 2>|

PID: 3242112 START: 1754971402796349184 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 END: 1754971402796387840 :git command None ['git', 'checkout', '-q', '-b', 'd2j', '89cf437b7f7140e8293bfa81a1273dd8cd15d944'] with debug: : git checkout -q -b d2j 89cf437b7f7140e8293bfa81a1273dd8cd15d944 2>|

PID: 3242107 START: 1754971402796421376 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242106 START: 1754971402796438528 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242111 START: 1754971402796438528 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242109 END: 1754971402796441600 :git command None ['git', 'update-ref', 'refs/heads/d2j', '664d7c6e5949da7e70a0184f06287d8e099cb0b3'] with debug: : git update-ref refs/heads/d2j 664d7c6e5949da7e70a0184f06287d8e099cb0b3 1>| 2>|

PID: 3242108 END: 1754971402796526592 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'dd63692b65c8043445c513edbc2513234c065feb'] with debug: : git update-ref refs/heads/d2j dd63692b65c8043445c513edbc2513234c065feb 1>| 2>|

PID: 3242109 START: 1754971402796599296 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 START: 1754971402796616960 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3242108 START: 1754971402796623360 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242105 END: 1754971402797244416 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 END: 1754971402797305600 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3242106 END: 1754971402797372160 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242108 END: 1754971402797501952 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242112 END: 1754971402797757184 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 START: 1754971402797770752 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/tombstone
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242108 START: 1754971402797782272 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J3/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402797785856 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/system/tools
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242107 END: 1754971402797845504 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242106 START: 1754971402797901568 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J3/minieye_sdk
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242111 END: 1754971402797936640 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242109 END: 1754971402798121216 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242107 START: 1754971402798156288 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J6E/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242112 START: 1754971402798285824 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/OAX8000/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242111 START: 1754971402798428160 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/SSC379G/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242109 START: 1754971402798552576 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 END: 1754971402798572032 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/tombstone
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 START: 1754971402798693632 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 END: 1754971402798737152 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/middleware/system/tools
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242106 END: 1754971402798797312 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J3/minieye_sdk
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402798873088 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242106 START: 1754971402798906112 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242108 END: 1754971402799013376 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J3/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242108 START: 1754971402799163904 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242112 END: 1754971402799332608 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/OAX8000/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242107 END: 1754971402799389696 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/J6E/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242109 END: 1754971402799426048 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 END: 1754971402799468032 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242110 START: 1754971402799508736 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3242112 START: 1754971402799505664 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 START: 1754971402799537920 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242109 START: 1754971402799547136 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 END: 1754971402799656448 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 START: 1754971402799699968 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3242110 END: 1754971402799796736 :: load refs /home/<USER>/mine/.repo/projects/middleware/tombstone.git

PID: 3242110 START: 1754971402799822592 :: parsing /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config

PID: 3242111 END: 1754971402799875584 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/SSC379G/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 END: 1754971402799939328 :: parsing /home/<USER>/mine/.repo/projects/middleware/tombstone.git/config

PID: 3242110 START: 1754971402800043520 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'b82935f6b767eedb3f03100978c2f96acfbb6910'] with debug: : git update-ref refs/heads/d2j b82935f6b767eedb3f03100978c2f96acfbb6910 1>| 2>|

PID: 3242111 START: 1754971402800057344 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242106 END: 1754971402800076288 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242106 START: 1754971402800146688 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3242105 END: 1754971402800216320 :: load refs /home/<USER>/mine/.repo/projects/middleware/system/tools.git

PID: 3242105 START: 1754971402800273408 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config

PID: 3242109 END: 1754971402800278784 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242109 START: 1754971402800322304 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3242112 END: 1754971402800352256 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242112 START: 1754971402800386304 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3242105 END: 1754971402800421632 :: parsing /home/<USER>/mine/.repo/projects/middleware/system/tools.git/config

PID: 3242108 END: 1754971402800449792 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242109 END: 1754971402800523520 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git

PID: 3242107 END: 1754971402800501248 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242108 START: 1754971402800530176 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3242109 START: 1754971402800550144 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config

PID: 3242107 START: 1754971402800566016 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3242106 END: 1754971402800605952 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git

PID: 3242109 END: 1754971402800637952 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/build.git/config

PID: 3242112 END: 1754971402800644096 :: load refs /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git

PID: 3242105 START: 1754971402800634880 :git command None ['git', 'update-ref', 'refs/heads/d2j', '96e7a9e632b48726ac76a34ae81b762fcdd0d669'] with debug: : git update-ref refs/heads/d2j 96e7a9e632b48726ac76a34ae81b762fcdd0d669 1>| 2>|

PID: 3242106 START: 1754971402800657152 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config

PID: 3242112 START: 1754971402800667648 :: parsing /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config

PID: 3242109 START: 1754971402800730368 :git command None ['git', 'update-ref', 'refs/heads/d2j', '8d6561a7de854ca03298d0cb86f4b71900a20b3a'] with debug: : git update-ref refs/heads/d2j 8d6561a7de854ca03298d0cb86f4b71900a20b3a 1>| 2>|

PID: 3242112 END: 1754971402800759808 :: parsing /home/<USER>/mine/.repo/projects/platform/OAX8000/build.git/config

PID: 3242106 END: 1754971402800785664 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/minieye_sdk.git/config

PID: 3242112 START: 1754971402800865792 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'd9dbc7ef09eacd869dd86d0b4084df5a8f615062'] with debug: : git update-ref refs/heads/d2j d9dbc7ef09eacd869dd86d0b4084df5a8f615062 1>| 2>|

PID: 3242107 END: 1754971402800892160 :: load refs /home/<USER>/mine/.repo/projects/platform/J6E/build.git

PID: 3242107 START: 1754971402800953600 :: parsing /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config

PID: 3242108 END: 1754971402800973568 :: load refs /home/<USER>/mine/.repo/projects/platform/J3/build.git

PID: 3242106 START: 1754971402800978944 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'afe9ee8e6b228dc8852816f25133361a42087a5a'] with debug: : git update-ref refs/heads/d2j afe9ee8e6b228dc8852816f25133361a42087a5a 1>| 2>|

PID: 3242108 START: 1754971402801030912 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/build.git/config

PID: 3242107 END: 1754971402801067264 :: parsing /home/<USER>/mine/.repo/projects/platform/J6E/build.git/config

PID: 3242110 END: 1754971402801091584 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'b82935f6b767eedb3f03100978c2f96acfbb6910'] with debug: : git update-ref refs/heads/d2j b82935f6b767eedb3f03100978c2f96acfbb6910 1>| 2>|

PID: 3242110 START: 1754971402801171200 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242108 END: 1754971402801161472 :: parsing /home/<USER>/mine/.repo/projects/platform/J3/build.git/config

PID: 3242107 START: 1754971402801178880 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'c8ef26455bca6a95edf1bdd418b0e464785ff650'] with debug: : git update-ref refs/heads/d2j c8ef26455bca6a95edf1bdd418b0e464785ff650 1>| 2>|

PID: 3242108 START: 1754971402801343488 :git command None ['git', 'update-ref', 'refs/heads/d2j', '32b39122b258ff51885756137984506085c7503e'] with debug: : git update-ref refs/heads/d2j 32b39122b258ff51885756137984506085c7503e 1>| 2>|

PID: 3242111 END: 1754971402801335296 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242111 START: 1754971402801413376 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3242105 END: 1754971402801720064 :git command None ['git', 'update-ref', 'refs/heads/d2j', '96e7a9e632b48726ac76a34ae81b762fcdd0d669'] with debug: : git update-ref refs/heads/d2j 96e7a9e632b48726ac76a34ae81b762fcdd0d669 1>| 2>|

PID: 3242111 END: 1754971402801786880 :: load refs /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git

PID: 3242111 START: 1754971402801838080 :: parsing /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config

PID: 3242105 START: 1754971402801887488 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242111 END: 1754971402801987584 :: parsing /home/<USER>/mine/.repo/projects/platform/SSC379G/build.git/config

PID: 3242111 START: 1754971402802141440 :git command None ['git', 'update-ref', 'refs/heads/d2j', '505603a055f740fbd754a7ea4483036e934ed5e3'] with debug: : git update-ref refs/heads/d2j 505603a055f740fbd754a7ea4483036e934ed5e3 1>| 2>|

PID: 3242107 END: 1754971402802214656 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'c8ef26455bca6a95edf1bdd418b0e464785ff650'] with debug: : git update-ref refs/heads/d2j c8ef26455bca6a95edf1bdd418b0e464785ff650 1>| 2>|

PID: 3242109 END: 1754971402802288640 :git command None ['git', 'update-ref', 'refs/heads/d2j', '8d6561a7de854ca03298d0cb86f4b71900a20b3a'] with debug: : git update-ref refs/heads/d2j 8d6561a7de854ca03298d0cb86f4b71900a20b3a 1>| 2>|

PID: 3242107 START: 1754971402802339584 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242109 START: 1754971402802466048 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242112 END: 1754971402802474496 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'd9dbc7ef09eacd869dd86d0b4084df5a8f615062'] with debug: : git update-ref refs/heads/d2j d9dbc7ef09eacd869dd86d0b4084df5a8f615062 1>| 2>|

PID: 3242108 END: 1754971402802495488 :git command None ['git', 'update-ref', 'refs/heads/d2j', '32b39122b258ff51885756137984506085c7503e'] with debug: : git update-ref refs/heads/d2j 32b39122b258ff51885756137984506085c7503e 1>| 2>|

PID: 3242110 END: 1754971402802577152 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242106 END: 1754971402802607360 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'afe9ee8e6b228dc8852816f25133361a42087a5a'] with debug: : git update-ref refs/heads/d2j afe9ee8e6b228dc8852816f25133361a42087a5a 1>| 2>|

PID: 3242112 START: 1754971402802622720 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242108 START: 1754971402802633984 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242106 START: 1754971402802731264 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 START: 1754971402802787584 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/minieye_sdk
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242111 END: 1754971402803209728 :git command None ['git', 'update-ref', 'refs/heads/d2j', '505603a055f740fbd754a7ea4483036e934ed5e3'] with debug: : git update-ref refs/heads/d2j 505603a055f740fbd754a7ea4483036e934ed5e3 1>| 2>|

PID: 3242111 START: 1754971402803315200 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242107 END: 1754971402803426816 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242105 END: 1754971402803486464 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242112 END: 1754971402803746304 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242107 START: 1754971402803785984 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/Ubuntu/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402803851008 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/product
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242106 END: 1754971402804014336 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 END: 1754971402804027648 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/T1Q3/minieye_sdk
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242109 END: 1754971402804032768 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242108 END: 1754971402804172288 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 START: 1754971402804190976 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242111 END: 1754971402804690432 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242105 END: 1754971402805086976 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/product
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242105 START: 1754971402805252608 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 END: 1754971402805291520 :git command None ['git', 'rev-parse', '--symbolic-full-name', 'HEAD'] with debug: 
: cd /home/<USER>/mine/platform/Ubuntu/build
: git rev-parse --symbolic-full-name HEAD 1>| 2>|

PID: 3242110 END: 1754971402805433856 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 START: 1754971402805453568 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242110 START: 1754971402805485056 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3242110 END: 1754971402805788672 :: load refs /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git

PID: 3242110 START: 1754971402805834752 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config

PID: 3242110 END: 1754971402805964544 :: parsing /home/<USER>/mine/.repo/projects/platform/T1Q3/minieye_sdk.git/config

PID: 3242110 START: 1754971402806131712 :git command None ['git', 'update-ref', 'refs/heads/d2j', '6d992620ca3f4ff8dc3ee01183673c18e1bb4268'] with debug: : git update-ref refs/heads/d2j 6d992620ca3f4ff8dc3ee01183673c18e1bb4268 1>| 2>|

PID: 3242107 END: 1754971402806443008 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242105 END: 1754971402806440448 :git command None ['git', 'rev-parse', 'HEAD'] with debug: : git rev-parse HEAD 1>| 2>|

PID: 3242107 START: 1754971402806477056 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3242105 START: 1754971402806493952 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3242107 END: 1754971402806723584 :: load refs /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git

PID: 3242107 START: 1754971402806751744 :: parsing /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config

PID: 3242105 END: 1754971402806777856 :: load refs /home/<USER>/mine/.repo/projects/product.git

PID: 3242105 START: 1754971402806828288 :: parsing /home/<USER>/mine/.repo/projects/product.git/config

PID: 3242107 END: 1754971402806834176 :: parsing /home/<USER>/mine/.repo/projects/platform/Ubuntu/build.git/config

PID: 3242107 START: 1754971402806934016 :git command None ['git', 'update-ref', 'refs/heads/d2j', '379178b1f11547d2fd009b2927278e9e90d8eb53'] with debug: : git update-ref refs/heads/d2j 379178b1f11547d2fd009b2927278e9e90d8eb53 1>| 2>|

PID: 3242105 END: 1754971402806965760 :: parsing /home/<USER>/mine/.repo/projects/product.git/config

PID: 3242105 START: 1754971402807126528 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'c033236a310cad9f44853c4b4064b9d7c4259aaa'] with debug: : git update-ref refs/heads/d2j c033236a310cad9f44853c4b4064b9d7c4259aaa 1>| 2>|

PID: 3242110 END: 1754971402807760640 :git command None ['git', 'update-ref', 'refs/heads/d2j', '6d992620ca3f4ff8dc3ee01183673c18e1bb4268'] with debug: : git update-ref refs/heads/d2j 6d992620ca3f4ff8dc3ee01183673c18e1bb4268 1>| 2>|

PID: 3242110 START: 1754971402807907072 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242107 END: 1754971402808381440 :git command None ['git', 'update-ref', 'refs/heads/d2j', '379178b1f11547d2fd009b2927278e9e90d8eb53'] with debug: : git update-ref refs/heads/d2j 379178b1f11547d2fd009b2927278e9e90d8eb53 1>| 2>|

PID: 3242107 START: 1754971402808461824 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242105 END: 1754971402808677376 :git command None ['git', 'update-ref', 'refs/heads/d2j', 'c033236a310cad9f44853c4b4064b9d7c4259aaa'] with debug: : git update-ref refs/heads/d2j c033236a310cad9f44853c4b4064b9d7c4259aaa 1>| 2>|

PID: 3242105 START: 1754971402808809728 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242110 END: 1754971402809457152 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242107 END: 1754971402809539328 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242105 END: 1754971402810231808 :git command None ['git', 'symbolic-ref', 'HEAD', 'refs/heads/d2j'] with debug: : git symbolic-ref HEAD refs/heads/d2j 1>| 2>|

PID: 3242098 START: 1754971402813225216 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3242098 END: 1754971402814278400 :git command None ['git', 'config', '--get', 'trace2.eventtarget'] with debug: : git config --get trace2.eventtarget 1>| 2>|

PID: 3242098 END: 1754971402814329344 :+++++++++++++++NEW COMMAND+++++++++++++++++++ starting new command: start, d2j, --all [sid=repo-20250812T040322Z-P00317872/repo-20250812T040322Z-P00317872]

