{"platform": "OAX8000", "build_type": "release", "subsystem": [{"name": "applications", "component": [{"name": "scantree", "dir": "//middleware/system/tools/scantree:scantree", "features": []}, {"name": "file_monitor", "dir": "//middleware/system/tools/filemonitor:file_monitor", "features": []}, {"name": "sample_threadpool", "dir": "//middleware/system/core/libcppbase/demo:sample_threadpool", "features": []}, {"name": "sample_message", "dir": "//middleware/system/core/libmessage/demo:sample_message", "features": []}], "bak": [{"name": "logd_group", "dir": "//middleware/logd/:logd_group", "features": []}, {"name": "system_res_monitor", "dir": "//middleware/system/tools/system_res_monitor:system_res_monitor", "features": []}, {"name": "cgroup_manager_group", "dir": "//applications/sensor_abstraction/cgroup_manager:cgroup_manager_group", "features": []}, {"name": "mlog", "dir": "//middleware/mlog/:mlog_group", "features": []}, {"name": "mlog_test", "dir": "//middleware/mlog/test:mlog_test", "features": []}, {"name": "mlog_sample", "dir": "//middleware/mlog/sample:mlog_sample", "features": []}, {"name": "daemon", "dir": "//middleware/daemon/:daemon_group", "features": []}, {"name": "daemon_test", "dir": "//middleware/daemon/test/:daemon_test", "features": []}, {"name": "persistency", "dir": "//middleware/persistency:persistency_group", "features": []}, {"name": "persistency_test", "dir": "//middleware/persistency/test:persistency_test", "features": []}, {"name": "persistency_sample", "dir": "//middleware/persistency/sample:persistency_sample", "features": []}, {"name": "logd_group", "dir": "//middleware/logd/:logd_group", "features": []}, {"name": "dumpsys_group", "dir": "//middleware/dumpsys/:dumpsys_group", "features": []}, {"name": "nanoserver", "dir": "//middleware/system/sample/:nano_server", "features": []}, {"name": "nanoclient", "dir": "//middleware/system/sample/:nano_client", "features": []}, {"name": "tombstone_group", "dir": "//middleware/tombstone/:tombstone_group", "features": []}]}]}